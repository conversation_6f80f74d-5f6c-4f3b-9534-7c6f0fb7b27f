import { CrudCasBase } from './CrudCasBase';
import type { <PERSON>rud<PERSON><PERSON> } from '../crud/types';
export interface CrudCasOptions {
    hash: (blob: Uint8Array) => Promise<string>;
}
export declare class CrudCas extends CrudCasBase<string> {
    protected readonly crud: Crud<PERSON>pi;
    protected readonly options: CrudCasOptions;
    constructor(crud: <PERSON>rud<PERSON><PERSON>, options: CrudCasOptions);
}
