{"version": 3, "file": "IonDecoderBase.js", "sourceRoot": "", "sources": ["../../src/ion/IonDecoderBase.ts"], "names": [], "mappings": ";;;;AAAA,iEAA4D;AAC5D,iIAAiG;AAIjG,qCAAgC;AAEhC,MAAa,cAAc;IAKzB,YAAY,MAAU;QACpB,IAAI,CAAC,MAAM,GAAG,CAAC,MAAM,IAAI,IAAI,eAAM,EAAE,CAAM,CAAC;QAC5C,IAAI,CAAC,WAAW,GAAG,iCAAuB,CAAC;IAC7C,CAAC;IAEM,GAAG;QACR,MAAM,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC,EAAE,EAAE,CAAC;QAClC,MAAM,IAAI,GAAG,CAAC,QAAQ,IAAI,CAAC,CAAC,GAAG,GAAG,CAAC;QACnC,MAAM,MAAM,GAAG,QAAQ,GAAG,GAAG,CAAC;QAE9B,QAAQ,IAAI,EAAE,CAAC;YACb;gBACE,OAAO,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;YAC/B;gBACE,OAAO,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;YAC/B;gBACE,OAAO,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;YAC/B;gBACE,OAAO,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;YAC/B;gBACE,OAAO,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;YAChC;gBACE,OAAO,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;YACjC;gBACE,OAAO,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;YACjC;gBACE,OAAO,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;YAC/B;gBACE,OAAO,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;YACjC;gBACE,OAAO,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;YACrC;gBACE,MAAM,IAAI,KAAK,CAAC,uBAAuB,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QAChE,CAAC;IACH,CAAC;IAES,QAAQ,CAAC,MAAc;QAC/B,IAAI,MAAM,KAAK,EAAE;YAAE,OAAO,IAAI,CAAC;QAC/B,IAAI,MAAM,KAAK,CAAC,EAAE,CAAC;YAEjB,IAAI,CAAC,GAAG,EAAE,CAAC;YACX,OAAO,IAAI,CAAC;QACd,CAAC;QACD,IAAI,MAAM,KAAK,EAAE,EAAE,CAAC;YAElB,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;YACnC,IAAI,CAAC,MAAM,CAAC,CAAC,IAAI,SAAS,CAAC;YAC3B,IAAI,CAAC,GAAG,EAAE,CAAC;YACX,OAAO,IAAI,CAAC;QACd,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,CAAC,IAAI,MAAM,CAAC;QACxB,IAAI,CAAC,GAAG,EAAE,CAAC;QACX,OAAO,IAAI,CAAC;IACd,CAAC;IAES,QAAQ,CAAC,MAAc;QAC/B,IAAI,MAAM,KAAK,EAAE;YAAE,OAAO,IAAI,CAAC;QAC/B,IAAI,MAAM,KAAK,CAAC;YAAE,OAAO,KAAK,CAAC;QAC/B,IAAI,MAAM,KAAK,CAAC;YAAE,OAAO,IAAI,CAAC;QAC9B,MAAM,IAAI,KAAK,CAAC,wBAAwB,MAAM,EAAE,CAAC,CAAC;IACpD,CAAC;IAES,QAAQ,CAAC,MAAc;QAC/B,IAAI,MAAM,KAAK,EAAE;YAAE,OAAO,IAAI,CAAC;QAC/B,IAAI,MAAM,KAAK,CAAC;YAAE,OAAO,CAAC,CAAC;QAE3B,IAAI,KAAK,GAAG,CAAC,CAAC;QACd,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YAChC,KAAK,GAAG,KAAK,GAAG,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,EAAE,EAAE,CAAC;QACzC,CAAC;QACD,OAAO,KAAK,CAAC;IACf,CAAC;IAES,QAAQ,CAAC,MAAc;QAC/B,IAAI,MAAM,KAAK,EAAE;YAAE,OAAO,IAAI,CAAC;QAC/B,IAAI,MAAM,KAAK,CAAC;YAAE,MAAM,IAAI,KAAK,CAAC,0BAA0B,CAAC,CAAC;QAE9D,IAAI,KAAK,GAAG,CAAC,CAAC;QACd,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YAChC,KAAK,GAAG,KAAK,GAAG,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,EAAE,EAAE,CAAC;QACzC,CAAC;QACD,OAAO,CAAC,KAAK,CAAC;IAChB,CAAC;IAES,SAAS,CAAC,MAAc;QAChC,IAAI,MAAM,KAAK,EAAE;YAAE,OAAO,IAAI,CAAC;QAC/B,IAAI,MAAM,KAAK,CAAC;YAAE,OAAO,GAAG,CAAC;QAC7B,IAAI,MAAM,KAAK,CAAC;YAAE,OAAO,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC;QAC3C,IAAI,MAAM,KAAK,CAAC;YAAE,OAAO,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC;QAC3C,MAAM,IAAI,KAAK,CAAC,6BAA6B,MAAM,EAAE,CAAC,CAAC;IACzD,CAAC;IAES,UAAU,CAAC,MAAc;QACjC,IAAI,MAAM,KAAK,EAAE;YAAE,OAAO,IAAI,CAAC;QAE/B,IAAI,YAAY,GAAG,MAAM,CAAC;QAC1B,IAAI,MAAM,KAAK,EAAE,EAAE,CAAC;YAClB,YAAY,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;QAClC,CAAC;QAED,IAAI,YAAY,KAAK,CAAC;YAAE,OAAO,EAAE,CAAC;QAElC,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;IACxC,CAAC;IAES,UAAU,CAAC,MAAc;QACjC,IAAI,MAAM,KAAK,EAAE;YAAE,OAAO,IAAI,CAAC;QAE/B,IAAI,YAAY,GAAG,MAAM,CAAC;QAC1B,IAAI,MAAM,KAAK,EAAE,EAAE,CAAC;YAClB,YAAY,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;QAClC,CAAC;QAED,IAAI,YAAY,KAAK,CAAC;YAAE,OAAO,IAAI,UAAU,CAAC,CAAC,CAAC,CAAC;QAEjD,OAAO,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;IACvC,CAAC;IAES,QAAQ,CAAC,MAAc;QAC/B,IAAI,MAAM,KAAK,EAAE;YAAE,OAAO,IAAI,CAAC;QAE/B,IAAI,YAAY,GAAG,MAAM,CAAC;QAC1B,IAAI,MAAM,KAAK,EAAE,EAAE,CAAC;YAClB,YAAY,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;QAClC,CAAC;QAED,IAAI,YAAY,KAAK,CAAC;YAAE,OAAO,EAAE,CAAC;QAElC,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,GAAG,YAAY,CAAC;QAC5C,MAAM,IAAI,GAAc,EAAE,CAAC;QAE3B,OAAO,IAAI,CAAC,MAAM,CAAC,CAAC,GAAG,MAAM,EAAE,CAAC;YAC9B,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC;QACxB,CAAC;QAED,IAAI,IAAI,CAAC,MAAM,CAAC,CAAC,KAAK,MAAM,EAAE,CAAC;YAC7B,MAAM,IAAI,KAAK,CAAC,sCAAsC,CAAC,CAAC;QAC1D,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAES,UAAU,CAAC,MAAc;QACjC,IAAI,MAAM,KAAK,EAAE;YAAE,OAAO,IAAI,CAAC;QAE/B,IAAI,YAAY,GAAG,MAAM,CAAC;QAC1B,IAAI,MAAM,KAAK,EAAE,EAAE,CAAC;YAClB,YAAY,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;QAClC,CAAC;QAED,IAAI,YAAY,KAAK,CAAC;YAAE,OAAO,EAAE,CAAC;QAElC,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,GAAG,YAAY,CAAC;QAC5C,MAAM,MAAM,GAA4B,EAAE,CAAC;QAE3C,OAAO,IAAI,CAAC,MAAM,CAAC,CAAC,GAAG,MAAM,EAAE,CAAC;YAC9B,MAAM,WAAW,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;YACrC,MAAM,SAAS,GAAG,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,CAAC;YAClD,MAAM,UAAU,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YAC9B,MAAM,CAAC,SAAS,CAAC,GAAG,UAAU,CAAC;QACjC,CAAC;QAED,IAAI,IAAI,CAAC,MAAM,CAAC,CAAC,KAAK,MAAM,EAAE,CAAC;YAC7B,MAAM,IAAI,KAAK,CAAC,wCAAwC,CAAC,CAAC;QAC5D,CAAC;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;IAES,cAAc,CAAC,MAAc;QACrC,IAAI,MAAM,GAAG,CAAC,EAAE,CAAC;YACf,MAAM,IAAI,KAAK,CAAC,+CAA+C,CAAC,CAAC;QACnE,CAAC;QAED,IAAI,YAAY,GAAG,MAAM,CAAC;QAC1B,IAAI,MAAM,KAAK,EAAE,EAAE,CAAC;YAClB,YAAY,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;QAClC,CAAC;QAED,MAAM,WAAW,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;QACrC,MAAM,WAAW,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,GAAG,WAAW,CAAC;QAGhD,OAAO,IAAI,CAAC,MAAM,CAAC,CAAC,GAAG,WAAW,EAAE,CAAC;YACnC,IAAI,CAAC,SAAS,EAAE,CAAC;QACnB,CAAC;QAED,IAAI,IAAI,CAAC,MAAM,CAAC,CAAC,KAAK,WAAW,EAAE,CAAC;YAClC,MAAM,IAAI,KAAK,CAAC,uDAAuD,CAAC,CAAC;QAC3E,CAAC;QAGD,OAAO,IAAI,CAAC,GAAG,EAAE,CAAC;IACpB,CAAC;IAES,SAAS;QACjB,IAAI,KAAK,GAAG,CAAC,CAAC;QACd,IAAI,IAAY,CAAC;QAEjB,GAAG,CAAC;YACF,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,EAAE,EAAE,CAAC;YACxB,KAAK,GAAG,CAAC,KAAK,IAAI,CAAC,CAAC,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC,CAAC;QACvC,CAAC,QAAQ,CAAC,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,EAAE;QAE9B,OAAO,KAAK,CAAC;IACf,CAAC;IAES,QAAQ;QAChB,MAAM,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC,EAAE,EAAE,CAAC;QAGnC,IAAI,SAAS,GAAG,IAAI,EAAE,CAAC;YACrB,MAAM,IAAI,GAAG,SAAS,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACvC,MAAM,SAAS,GAAG,SAAS,GAAG,IAAI,CAAC;YACnC,OAAO,IAAI,GAAG,SAAS,CAAC;QAC1B,CAAC;QAGD,MAAM,IAAI,GAAG,SAAS,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACvC,IAAI,SAAS,GAAG,SAAS,GAAG,IAAI,CAAC;QACjC,IAAI,IAAY,CAAC;QAEjB,GAAG,CAAC;YACF,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,EAAE,EAAE,CAAC;YACxB,SAAS,GAAG,CAAC,SAAS,IAAI,CAAC,CAAC,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC,CAAC;QAC/C,CAAC,QAAQ,CAAC,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,EAAE;QAE9B,OAAO,IAAI,GAAG,SAAS,CAAC;IAC1B,CAAC;IAES,aAAa,CAAC,QAAgB;QACtC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;YAClB,MAAM,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAC;QAC/C,CAAC;QAED,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QAC9C,IAAI,MAAM,KAAK,SAAS,EAAE,CAAC;YACzB,MAAM,IAAI,KAAK,CAAC,sBAAsB,QAAQ,EAAE,CAAC,CAAC;QACpD,CAAC;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;IAES,WAAW;QACnB,MAAM,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC;QAC9B,IAAI,GAAG,KAAK,UAAU,EAAE,CAAC;YACvB,MAAM,IAAI,KAAK,CAAC,wCAAwC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QAC9E,CAAC;IACH,CAAC;IAES,eAAe;QAEvB,IAAI,IAAI,CAAC,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC;YAC7C,MAAM,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC;YACpC,MAAM,IAAI,GAAG,CAAC,QAAQ,IAAI,CAAC,CAAC,GAAG,GAAG,CAAC;YAEnC,IAAI,IAAI,OAAc,EAAE,CAAC;gBAEvB,MAAM,UAAU,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;gBAG9B,IAAI,UAAU,IAAI,OAAO,UAAU,KAAK,QAAQ,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE,CAAC;oBAC/E,MAAM,UAAU,GAAG,SAAS,CAAC;oBAC7B,MAAM,GAAG,GAAG,UAAqC,CAAC;oBAElD,IAAI,UAAU,IAAI,GAAG,IAAI,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC,EAAE,CAAC;wBAExD,MAAM,UAAU,GAAG,GAAG,CAAC,UAAU,CAAa,CAAC;wBAC/C,IAAI,CAAC,OAAO,GAAG,IAAI,eAAM,CAAC,IAAI,CAAC,OAAO,IAAI,IAAI,EAAE,UAAU,CAAC,CAAC;oBAC9D,CAAC;gBACH,CAAC;YACH,CAAC;QACH,CAAC;IACH,CAAC;CACF;AAxRD,wCAwRC"}