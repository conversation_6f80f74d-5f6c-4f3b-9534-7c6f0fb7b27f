{"name": "ggcasecatalogs", "version": "1.0.0", "description": "haiya back to native with webpack", "main": "index.js", "scripts": {"build": "webpack"}, "author": "GGCatalogTeam", "license": "ISC", "devDependencies": {"concurrently": "^9.2.0", "nodemon": "^3.1.10", "webpack": "^5.101.0", "webpack-cli": "^6.0.1", "webpack-dev-server": "^5.2.2", "webpack-merge": "^6.0.1"}, "dependencies": {"axios": "^1.11.0", "bcrypt": "^6.0.0", "cors": "^2.8.5", "dotenv": "^17.2.1", "express": "^5.1.0", "express-rate-limit": "^8.0.1", "express-session": "^1.18.2", "express-validator": "^7.2.1", "helmet": "^8.1.0", "mysql2": "^3.14.2", "qrcode": "^1.5.4"}}