import type { <PERSON><PERSON><PERSON>r, IWriterGrowable } from '@jsonjoy.com/util/lib/buffers';
import type { BinaryJsonEncoder, StreamingBinaryJsonEncoder } from '../types';
export declare class UbjsonEncoder implements BinaryJsonEncoder, StreamingBinaryJsonEncoder {
    readonly writer: IWriter & IWriterGrowable;
    constructor(writer: IWriter & IWriterGrowable);
    encode(value: unknown): Uint8Array;
    writeAny(value: unknown): void;
    writeNull(): void;
    writeUndef(): void;
    writeBoolean(bool: boolean): void;
    writeNumber(num: number): void;
    writeInteger(int: number): void;
    writeUInteger(uint: number): void;
    writeFloat(float: number): void;
    writeBigInt(int: bigint): void;
    writeBin(buf: Uint8Array): void;
    writeStr(str: string): void;
    writeAsciiStr(str: string): void;
    writeArr(arr: unknown[]): void;
    writeObj(obj: Record<string, unknown>): void;
    writeKey(str: string): void;
    writeStartStr(): void;
    writeStrChunk(str: string): void;
    writeEndStr(): void;
    writeStartBin(): void;
    writeBinChunk(buf: Uint8Array): void;
    writeEndBin(): void;
    writeStartArr(): void;
    writeArrChunk(item: unknown): void;
    writeEndArr(): void;
    writeStartObj(): void;
    writeObjChunk(key: string, value: unknown): void;
    writeEndObj(): void;
}
