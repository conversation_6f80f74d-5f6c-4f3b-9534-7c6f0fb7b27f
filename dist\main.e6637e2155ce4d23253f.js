(()=>{"use strict";var n,e={171:(n,e,t)=>{t.d(e,{A:()=>s});var r=t(354),a=t.n(r),i=t(314),o=t.n(i)()(a());o.push([n.id,"/* Reset and base styles */\n* {\n  margin: 0;\n  padding: 0;\n  box-sizing: border-box;\n}\n\nhtml, body {\n  height: 100%;\n  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;\n  font-size: 14px;\n  line-height: 1.5;\n  color: #1f2937;\n  background-color: #f9fafb;\n}\n\n#root {\n  height: 100%;\n  display: flex;\n  flex-direction: column;\n}\n\n/* Typography */\nh1, h2, h3, h4, h5, h6 {\n  font-weight: 600;\n  line-height: 1.25;\n  margin-bottom: 0.5rem;\n}\n\nh1 { font-size: 2rem; }\nh2 { font-size: 1.5rem; }\nh3 { font-size: 1.25rem; }\nh4 { font-size: 1.125rem; }\nh5 { font-size: 1rem; }\nh6 { font-size: 0.875rem; }\n\np {\n  margin-bottom: 1rem;\n}\n\n/* Links */\na {\n  color: #3b82f6;\n  text-decoration: none;\n  transition: color 0.2s;\n}\n\na:hover {\n  color: #2563eb;\n  text-decoration: underline;\n}\n\n/* Buttons */\n.btn {\n  display: inline-flex;\n  align-items: center;\n  justify-content: center;\n  padding: 0.5rem 1rem;\n  border: 1px solid transparent;\n  border-radius: 0.375rem;\n  font-size: 0.875rem;\n  font-weight: 500;\n  text-decoration: none;\n  cursor: pointer;\n  transition: all 0.2s;\n  gap: 0.5rem;\n}\n\n.btn:disabled {\n  opacity: 0.5;\n  cursor: not-allowed;\n}\n\n.btn-primary {\n  background-color: #3b82f6;\n  color: white;\n  border-color: #3b82f6;\n}\n\n.btn-primary:hover:not(:disabled) {\n  background-color: #2563eb;\n  border-color: #2563eb;\n  text-decoration: none;\n  color: white;\n}\n\n.btn-secondary {\n  background-color: #6b7280;\n  color: white;\n  border-color: #6b7280;\n}\n\n.btn-secondary:hover:not(:disabled) {\n  background-color: #4b5563;\n  border-color: #4b5563;\n  text-decoration: none;\n  color: white;\n}\n\n.btn-danger {\n  background-color: #ef4444;\n  color: white;\n  border-color: #ef4444;\n}\n\n.btn-danger:hover:not(:disabled) {\n  background-color: #dc2626;\n  border-color: #dc2626;\n  text-decoration: none;\n  color: white;\n}\n\n.btn-outline {\n  background-color: transparent;\n  color: #374151;\n  border-color: #d1d5db;\n}\n\n.btn-outline:hover:not(:disabled) {\n  background-color: #f9fafb;\n  text-decoration: none;\n  color: #374151;\n}\n\n.btn-sm {\n  padding: 0.25rem 0.75rem;\n  font-size: 0.75rem;\n}\n\n.btn-lg {\n  padding: 0.75rem 1.5rem;\n  font-size: 1rem;\n}\n\n/* Forms */\n.form-group {\n  margin-bottom: 1rem;\n}\n\n.form-label {\n  display: block;\n  font-weight: 500;\n  margin-bottom: 0.25rem;\n  color: #374151;\n}\n\n.form-control {\n  display: block;\n  width: 100%;\n  padding: 0.5rem 0.75rem;\n  border: 1px solid #d1d5db;\n  border-radius: 0.375rem;\n  font-size: 0.875rem;\n  line-height: 1.5;\n  color: #1f2937;\n  background-color: white;\n  transition: border-color 0.2s, box-shadow 0.2s;\n}\n\n.form-control:focus {\n  outline: none;\n  border-color: #3b82f6;\n  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);\n}\n\n.form-control:disabled {\n  background-color: #f9fafb;\n  color: #6b7280;\n}\n\n.form-control.is-invalid {\n  border-color: #ef4444;\n}\n\n.form-control.is-invalid:focus {\n  border-color: #ef4444;\n  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);\n}\n\n.invalid-feedback {\n  display: block;\n  width: 100%;\n  margin-top: 0.25rem;\n  font-size: 0.75rem;\n  color: #ef4444;\n}\n\n/* Utilities */\n.text-center { text-align: center; }\n.text-left { text-align: left; }\n.text-right { text-align: right; }\n\n.d-none { display: none !important; }\n.d-block { display: block !important; }\n.d-flex { display: flex !important; }\n.d-inline-flex { display: inline-flex !important; }\n\n.justify-content-center { justify-content: center; }\n.justify-content-between { justify-content: space-between; }\n.justify-content-end { justify-content: flex-end; }\n\n.align-items-center { align-items: center; }\n.align-items-start { align-items: flex-start; }\n.align-items-end { align-items: flex-end; }\n\n.flex-column { flex-direction: column; }\n.flex-wrap { flex-wrap: wrap; }\n.flex-1 { flex: 1; }\n\n.gap-1 { gap: 0.25rem; }\n.gap-2 { gap: 0.5rem; }\n.gap-3 { gap: 0.75rem; }\n.gap-4 { gap: 1rem; }","",{version:3,sources:["webpack://./src/styles/global.css"],names:[],mappings:"AAAA,0BAA0B;AAC1B;EACE,SAAS;EACT,UAAU;EACV,sBAAsB;AACxB;;AAEA;EACE,YAAY;EACZ,0HAA0H;EAC1H,eAAe;EACf,gBAAgB;EAChB,cAAc;EACd,yBAAyB;AAC3B;;AAEA;EACE,YAAY;EACZ,aAAa;EACb,sBAAsB;AACxB;;AAEA,eAAe;AACf;EACE,gBAAgB;EAChB,iBAAiB;EACjB,qBAAqB;AACvB;;AAEA,KAAK,eAAe,EAAE;AACtB,KAAK,iBAAiB,EAAE;AACxB,KAAK,kBAAkB,EAAE;AACzB,KAAK,mBAAmB,EAAE;AAC1B,KAAK,eAAe,EAAE;AACtB,KAAK,mBAAmB,EAAE;;AAE1B;EACE,mBAAmB;AACrB;;AAEA,UAAU;AACV;EACE,cAAc;EACd,qBAAqB;EACrB,sBAAsB;AACxB;;AAEA;EACE,cAAc;EACd,0BAA0B;AAC5B;;AAEA,YAAY;AACZ;EACE,oBAAoB;EACpB,mBAAmB;EACnB,uBAAuB;EACvB,oBAAoB;EACpB,6BAA6B;EAC7B,uBAAuB;EACvB,mBAAmB;EACnB,gBAAgB;EAChB,qBAAqB;EACrB,eAAe;EACf,oBAAoB;EACpB,WAAW;AACb;;AAEA;EACE,YAAY;EACZ,mBAAmB;AACrB;;AAEA;EACE,yBAAyB;EACzB,YAAY;EACZ,qBAAqB;AACvB;;AAEA;EACE,yBAAyB;EACzB,qBAAqB;EACrB,qBAAqB;EACrB,YAAY;AACd;;AAEA;EACE,yBAAyB;EACzB,YAAY;EACZ,qBAAqB;AACvB;;AAEA;EACE,yBAAyB;EACzB,qBAAqB;EACrB,qBAAqB;EACrB,YAAY;AACd;;AAEA;EACE,yBAAyB;EACzB,YAAY;EACZ,qBAAqB;AACvB;;AAEA;EACE,yBAAyB;EACzB,qBAAqB;EACrB,qBAAqB;EACrB,YAAY;AACd;;AAEA;EACE,6BAA6B;EAC7B,cAAc;EACd,qBAAqB;AACvB;;AAEA;EACE,yBAAyB;EACzB,qBAAqB;EACrB,cAAc;AAChB;;AAEA;EACE,wBAAwB;EACxB,kBAAkB;AACpB;;AAEA;EACE,uBAAuB;EACvB,eAAe;AACjB;;AAEA,UAAU;AACV;EACE,mBAAmB;AACrB;;AAEA;EACE,cAAc;EACd,gBAAgB;EAChB,sBAAsB;EACtB,cAAc;AAChB;;AAEA;EACE,cAAc;EACd,WAAW;EACX,uBAAuB;EACvB,yBAAyB;EACzB,uBAAuB;EACvB,mBAAmB;EACnB,gBAAgB;EAChB,cAAc;EACd,uBAAuB;EACvB,8CAA8C;AAChD;;AAEA;EACE,aAAa;EACb,qBAAqB;EACrB,6CAA6C;AAC/C;;AAEA;EACE,yBAAyB;EACzB,cAAc;AAChB;;AAEA;EACE,qBAAqB;AACvB;;AAEA;EACE,qBAAqB;EACrB,4CAA4C;AAC9C;;AAEA;EACE,cAAc;EACd,WAAW;EACX,mBAAmB;EACnB,kBAAkB;EAClB,cAAc;AAChB;;AAEA,cAAc;AACd,eAAe,kBAAkB,EAAE;AACnC,aAAa,gBAAgB,EAAE;AAC/B,cAAc,iBAAiB,EAAE;;AAEjC,UAAU,wBAAwB,EAAE;AACpC,WAAW,yBAAyB,EAAE;AACtC,UAAU,wBAAwB,EAAE;AACpC,iBAAiB,+BAA+B,EAAE;;AAElD,0BAA0B,uBAAuB,EAAE;AACnD,2BAA2B,8BAA8B,EAAE;AAC3D,uBAAuB,yBAAyB,EAAE;;AAElD,sBAAsB,mBAAmB,EAAE;AAC3C,qBAAqB,uBAAuB,EAAE;AAC9C,mBAAmB,qBAAqB,EAAE;;AAE1C,eAAe,sBAAsB,EAAE;AACvC,aAAa,eAAe,EAAE;AAC9B,UAAU,OAAO,EAAE;;AAEnB,SAAS,YAAY,EAAE;AACvB,SAAS,WAAW,EAAE;AACtB,SAAS,YAAY,EAAE;AACvB,SAAS,SAAS,EAAE",sourcesContent:["/* Reset and base styles */\r\n* {\r\n  margin: 0;\r\n  padding: 0;\r\n  box-sizing: border-box;\r\n}\r\n\r\nhtml, body {\r\n  height: 100%;\r\n  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;\r\n  font-size: 14px;\r\n  line-height: 1.5;\r\n  color: #1f2937;\r\n  background-color: #f9fafb;\r\n}\r\n\r\n#root {\r\n  height: 100%;\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n/* Typography */\r\nh1, h2, h3, h4, h5, h6 {\r\n  font-weight: 600;\r\n  line-height: 1.25;\r\n  margin-bottom: 0.5rem;\r\n}\r\n\r\nh1 { font-size: 2rem; }\r\nh2 { font-size: 1.5rem; }\r\nh3 { font-size: 1.25rem; }\r\nh4 { font-size: 1.125rem; }\r\nh5 { font-size: 1rem; }\r\nh6 { font-size: 0.875rem; }\r\n\r\np {\r\n  margin-bottom: 1rem;\r\n}\r\n\r\n/* Links */\r\na {\r\n  color: #3b82f6;\r\n  text-decoration: none;\r\n  transition: color 0.2s;\r\n}\r\n\r\na:hover {\r\n  color: #2563eb;\r\n  text-decoration: underline;\r\n}\r\n\r\n/* Buttons */\r\n.btn {\r\n  display: inline-flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  padding: 0.5rem 1rem;\r\n  border: 1px solid transparent;\r\n  border-radius: 0.375rem;\r\n  font-size: 0.875rem;\r\n  font-weight: 500;\r\n  text-decoration: none;\r\n  cursor: pointer;\r\n  transition: all 0.2s;\r\n  gap: 0.5rem;\r\n}\r\n\r\n.btn:disabled {\r\n  opacity: 0.5;\r\n  cursor: not-allowed;\r\n}\r\n\r\n.btn-primary {\r\n  background-color: #3b82f6;\r\n  color: white;\r\n  border-color: #3b82f6;\r\n}\r\n\r\n.btn-primary:hover:not(:disabled) {\r\n  background-color: #2563eb;\r\n  border-color: #2563eb;\r\n  text-decoration: none;\r\n  color: white;\r\n}\r\n\r\n.btn-secondary {\r\n  background-color: #6b7280;\r\n  color: white;\r\n  border-color: #6b7280;\r\n}\r\n\r\n.btn-secondary:hover:not(:disabled) {\r\n  background-color: #4b5563;\r\n  border-color: #4b5563;\r\n  text-decoration: none;\r\n  color: white;\r\n}\r\n\r\n.btn-danger {\r\n  background-color: #ef4444;\r\n  color: white;\r\n  border-color: #ef4444;\r\n}\r\n\r\n.btn-danger:hover:not(:disabled) {\r\n  background-color: #dc2626;\r\n  border-color: #dc2626;\r\n  text-decoration: none;\r\n  color: white;\r\n}\r\n\r\n.btn-outline {\r\n  background-color: transparent;\r\n  color: #374151;\r\n  border-color: #d1d5db;\r\n}\r\n\r\n.btn-outline:hover:not(:disabled) {\r\n  background-color: #f9fafb;\r\n  text-decoration: none;\r\n  color: #374151;\r\n}\r\n\r\n.btn-sm {\r\n  padding: 0.25rem 0.75rem;\r\n  font-size: 0.75rem;\r\n}\r\n\r\n.btn-lg {\r\n  padding: 0.75rem 1.5rem;\r\n  font-size: 1rem;\r\n}\r\n\r\n/* Forms */\r\n.form-group {\r\n  margin-bottom: 1rem;\r\n}\r\n\r\n.form-label {\r\n  display: block;\r\n  font-weight: 500;\r\n  margin-bottom: 0.25rem;\r\n  color: #374151;\r\n}\r\n\r\n.form-control {\r\n  display: block;\r\n  width: 100%;\r\n  padding: 0.5rem 0.75rem;\r\n  border: 1px solid #d1d5db;\r\n  border-radius: 0.375rem;\r\n  font-size: 0.875rem;\r\n  line-height: 1.5;\r\n  color: #1f2937;\r\n  background-color: white;\r\n  transition: border-color 0.2s, box-shadow 0.2s;\r\n}\r\n\r\n.form-control:focus {\r\n  outline: none;\r\n  border-color: #3b82f6;\r\n  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);\r\n}\r\n\r\n.form-control:disabled {\r\n  background-color: #f9fafb;\r\n  color: #6b7280;\r\n}\r\n\r\n.form-control.is-invalid {\r\n  border-color: #ef4444;\r\n}\r\n\r\n.form-control.is-invalid:focus {\r\n  border-color: #ef4444;\r\n  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);\r\n}\r\n\r\n.invalid-feedback {\r\n  display: block;\r\n  width: 100%;\r\n  margin-top: 0.25rem;\r\n  font-size: 0.75rem;\r\n  color: #ef4444;\r\n}\r\n\r\n/* Utilities */\r\n.text-center { text-align: center; }\r\n.text-left { text-align: left; }\r\n.text-right { text-align: right; }\r\n\r\n.d-none { display: none !important; }\r\n.d-block { display: block !important; }\r\n.d-flex { display: flex !important; }\r\n.d-inline-flex { display: inline-flex !important; }\r\n\r\n.justify-content-center { justify-content: center; }\r\n.justify-content-between { justify-content: space-between; }\r\n.justify-content-end { justify-content: flex-end; }\r\n\r\n.align-items-center { align-items: center; }\r\n.align-items-start { align-items: flex-start; }\r\n.align-items-end { align-items: flex-end; }\r\n\r\n.flex-column { flex-direction: column; }\r\n.flex-wrap { flex-wrap: wrap; }\r\n.flex-1 { flex: 1; }\r\n\r\n.gap-1 { gap: 0.25rem; }\r\n.gap-2 { gap: 0.5rem; }\r\n.gap-3 { gap: 0.75rem; }\r\n.gap-4 { gap: 1rem; }"],sourceRoot:""}]);const s=o},744:(n,e,t)=>{var r=t(72),a=t.n(r),i=t(825),o=t.n(i),s=t(659),c=t.n(s),d=t(56),l=t.n(d),u=t(540),m=t.n(u),p=t(113),f=t.n(p),h=t(171),g={};g.styleTagTransform=f(),g.setAttributes=l(),g.insert=c().bind(null,"head"),g.domAPI=o(),g.insertStyleElement=m();a()(h.A,g);h.A&&h.A.locals&&h.A.locals;var b=t(29),v=t(901),y=t(75),A=t(467),x=t(756),w=t.n(x),k=t(284),B=t(848);function E(n,e){var t=Object.keys(n);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(n);e&&(r=r.filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})),t.push.apply(t,r)}return t}function C(n){for(var e=1;e<arguments.length;e++){var t=null!=arguments[e]?arguments[e]:{};e%2?E(Object(t),!0).forEach(function(e){(0,B.A)(n,e,t[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(n,Object.getOwnPropertyDescriptors(t)):E(Object(t)).forEach(function(e){Object.defineProperty(n,e,Object.getOwnPropertyDescriptor(t,e))})}return n}var S=function(){return(0,v.A)(function n(){(0,b.A)(this,n),this.baseURL="/api",this.defaultHeaders={"Content-Type":"application/json"}},[{key:"request",value:(l=(0,A.A)(w().mark(function n(e){var t,r,a,i,o,s,c=arguments;return w().wrap(function(n){for(;;)switch(n.prev=n.next){case 0:return t=c.length>1&&void 0!==c[1]?c[1]:{},r="".concat(this.baseURL).concat(e),a=C({headers:C(C({},this.defaultHeaders),t.headers),credentials:"include"},t),n.prev=1,n.next=2,fetch(r,a);case 2:if(i=n.sent,!(o=i.headers.get("content-type"))||!o.includes("application/json")){n.next=4;break}return n.next=3,i.json();case 3:s=n.sent,n.next=6;break;case 4:return n.next=5,i.text();case 5:s=n.sent;case 6:if(i.ok){n.next=8;break}if("object"!==(0,k.A)(s)||!s.message){n.next=7;break}throw new Error(s.message);case 7:throw new Error("HTTP ".concat(i.status,": ").concat(i.statusText));case 8:return n.abrupt("return",s);case 9:throw n.prev=9,n.catch(1);case 10:case"end":return n.stop()}},n,this,[[1,9]])})),function(n){return l.apply(this,arguments)})},{key:"get",value:(d=(0,A.A)(w().mark(function n(e){var t,r,a=arguments;return w().wrap(function(n){for(;;)switch(n.prev=n.next){case 0:return t=new URLSearchParams(a.length>1&&void 0!==a[1]?a[1]:{}).toString(),r=t?"".concat(e,"?").concat(t):e,n.abrupt("return",this.request(r,{method:"GET"}));case 1:case"end":return n.stop()}},n,this)})),function(n){return d.apply(this,arguments)})},{key:"post",value:(c=(0,A.A)(w().mark(function n(e){var t,r=arguments;return w().wrap(function(n){for(;;)switch(n.prev=n.next){case 0:return t=r.length>1&&void 0!==r[1]?r[1]:{},n.abrupt("return",this.request(e,{method:"POST",body:JSON.stringify(t)}));case 1:case"end":return n.stop()}},n,this)})),function(n){return c.apply(this,arguments)})},{key:"put",value:(s=(0,A.A)(w().mark(function n(e){var t,r=arguments;return w().wrap(function(n){for(;;)switch(n.prev=n.next){case 0:return t=r.length>1&&void 0!==r[1]?r[1]:{},n.abrupt("return",this.request(e,{method:"PUT",body:JSON.stringify(t)}));case 1:case"end":return n.stop()}},n,this)})),function(n){return s.apply(this,arguments)})},{key:"delete",value:(o=(0,A.A)(w().mark(function n(e){return w().wrap(function(n){for(;;)switch(n.prev=n.next){case 0:return n.abrupt("return",this.request(e,{method:"DELETE"}));case 1:case"end":return n.stop()}},n,this)})),function(n){return o.apply(this,arguments)})},{key:"uploadFile",value:(i=(0,A.A)(w().mark(function n(e,t){return w().wrap(function(n){for(;;)switch(n.prev=n.next){case 0:return n.abrupt("return",this.request(e,{method:"POST",body:t,headers:{}}));case 1:case"end":return n.stop()}},n,this)})),function(n,e){return i.apply(this,arguments)})},{key:"getAll",value:(a=(0,A.A)(w().mark(function n(e){return w().wrap(function(n){for(;;)switch(n.prev=n.next){case 0:return n.abrupt("return",this.get("/".concat(e)));case 1:case"end":return n.stop()}},n,this)})),function(n){return a.apply(this,arguments)})},{key:"getById",value:(r=(0,A.A)(w().mark(function n(e,t){return w().wrap(function(n){for(;;)switch(n.prev=n.next){case 0:return n.abrupt("return",this.get("/".concat(e,"/").concat(t)));case 1:case"end":return n.stop()}},n,this)})),function(n,e){return r.apply(this,arguments)})},{key:"create",value:(t=(0,A.A)(w().mark(function n(e,t){return w().wrap(function(n){for(;;)switch(n.prev=n.next){case 0:return n.abrupt("return",this.post("/".concat(e),t));case 1:case"end":return n.stop()}},n,this)})),function(n,e){return t.apply(this,arguments)})},{key:"update",value:(e=(0,A.A)(w().mark(function n(e,t,r){return w().wrap(function(n){for(;;)switch(n.prev=n.next){case 0:return n.abrupt("return",this.put("/".concat(e,"/").concat(t),r));case 1:case"end":return n.stop()}},n,this)})),function(n,t,r){return e.apply(this,arguments)})},{key:"remove",value:(n=(0,A.A)(w().mark(function n(e,t){return w().wrap(function(n){for(;;)switch(n.prev=n.next){case 0:return n.abrupt("return",this.delete("/".concat(e,"/").concat(t)));case 1:case"end":return n.stop()}},n,this)})),function(e,t){return n.apply(this,arguments)})}]);var n,e,t,r,a,i,o,s,c,d,l}(),L=function(){return(0,v.A)(function n(){(0,b.A)(this,n),this.apiService=new S,this.isAuthenticated=!1,this.user=null,this.authStateListeners=[],this.loginAttempts=0,this.maxLoginAttempts=5,this.lockoutTime=9e5,this.lockoutUntil=null},[{key:"checkAuth",value:(t=(0,A.A)(w().mark(function n(){var e;return w().wrap(function(n){for(;;)switch(n.prev=n.next){case 0:return n.prev=0,n.next=1,this.apiService.get("/admin/session");case 1:if(!(e=n.sent).success||!e.data.authenticated){n.next=2;break}return this.isAuthenticated=!0,this.user=e.data.user,this.notifyAuthStateChange(!0),n.abrupt("return",!0);case 2:return this.isAuthenticated=!1,this.user=null,this.notifyAuthStateChange(!1),n.abrupt("return",!1);case 3:n.next=5;break;case 4:return n.prev=4,n.catch(0),this.isAuthenticated=!1,this.user=null,this.notifyAuthStateChange(!1),n.abrupt("return",!1);case 5:case"end":return n.stop()}},n,this,[[0,4]])})),function(){return t.apply(this,arguments)})},{key:"login",value:(e=(0,A.A)(w().mark(function n(e,t){var r,a,i;return w().wrap(function(n){for(;;)switch(n.prev=n.next){case 0:if(!this.isAccountLocked()){n.next=1;break}throw r=Math.ceil((this.lockoutUntil-Date.now())/1e3/60),new Error("Account locked. Try again in ".concat(r," minutes."));case 1:return n.prev=1,n.next=2,this.apiService.post("/admin/login",{username:e,password:t});case 2:if(!(a=n.sent).success){n.next=3;break}return this.isAuthenticated=!0,this.user=a.data.user,this.loginAttempts=0,this.lockoutUntil=null,this.notifyAuthStateChange(!0),n.abrupt("return",a);case 3:throw this.handleFailedLogin(),new Error(a.message||"Login failed");case 4:n.next=6;break;case 5:throw n.prev=5,i=n.catch(1),this.handleFailedLogin(),i;case 6:case"end":return n.stop()}},n,this,[[1,5]])})),function(n,t){return e.apply(this,arguments)})},{key:"logout",value:(n=(0,A.A)(w().mark(function n(){return w().wrap(function(n){for(;;)switch(n.prev=n.next){case 0:return n.prev=0,n.next=1,this.apiService.post("/admin/logout");case 1:n.next=3;break;case 2:n.prev=2,n.catch(0);case 3:return n.prev=3,this.isAuthenticated=!1,this.user=null,this.notifyAuthStateChange(!1),n.finish(3);case 4:case"end":return n.stop()}},n,this,[[0,2,3,4]])})),function(){return n.apply(this,arguments)})},{key:"handleFailedLogin",value:function(){this.loginAttempts++,this.loginAttempts>=this.maxLoginAttempts&&(this.lockoutUntil=Date.now()+this.lockoutTime,localStorage.setItem("lockoutUntil",this.lockoutUntil.toString()))}},{key:"isAccountLocked",value:function(){var n=localStorage.getItem("lockoutUntil");if(n){if(this.lockoutUntil=parseInt(n),Date.now()<this.lockoutUntil)return!0;localStorage.removeItem("lockoutUntil"),this.lockoutUntil=null,this.loginAttempts=0}return!1}},{key:"getRemainingAttempts",value:function(){return Math.max(0,this.maxLoginAttempts-this.loginAttempts)}},{key:"getLockoutTimeRemaining",value:function(){return this.isAccountLocked()?Math.max(0,this.lockoutUntil-Date.now()):0}},{key:"onAuthStateChange",value:function(n){var e=this;return this.authStateListeners.push(n),function(){var t=e.authStateListeners.indexOf(n);t>-1&&e.authStateListeners.splice(t,1)}}},{key:"notifyAuthStateChange",value:function(n){var e=this;this.authStateListeners.forEach(function(t){try{t(n,e.user)}catch(n){}})}},{key:"getUser",value:function(){return this.user}},{key:"isLoggedIn",value:function(){return this.isAuthenticated}}]);var n,e,t}(),F=function(){return(0,v.A)(function n(){(0,b.A)(this,n),this.container=null,this.notifications=[],this.nextId=1},[{key:"init",value:function(){this.container=document.getElementById("notification-root"),this.container&&this.addStyles()}},{key:"addStyles",value:function(){if(!document.getElementById("notification-styles")){var n=document.createElement("style");n.id="notification-styles",n.textContent="\n      .notification-container {\n        position: fixed;\n        top: 20px;\n        right: 20px;\n        z-index: 9999;\n        max-width: 400px;\n        pointer-events: none;\n      }\n      \n      .notification {\n        background: white;\n        border-radius: 8px;\n        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);\n        margin-bottom: 12px;\n        padding: 16px;\n        border-left: 4px solid;\n        pointer-events: auto;\n        transform: translateX(100%);\n        opacity: 0;\n        transition: all 0.3s ease;\n        display: flex;\n        align-items: flex-start;\n        gap: 12px;\n        max-width: 100%;\n        word-wrap: break-word;\n      }\n      \n      .notification.show {\n        transform: translateX(0);\n        opacity: 1;\n      }\n      \n      .notification.success {\n        border-left-color: #10b981;\n      }\n      \n      .notification.error {\n        border-left-color: #ef4444;\n      }\n      \n      .notification.warning {\n        border-left-color: #f59e0b;\n      }\n      \n      .notification.info {\n        border-left-color: #3b82f6;\n      }\n      \n      .notification-icon {\n        flex-shrink: 0;\n        width: 20px;\n        height: 20px;\n        margin-top: 2px;\n      }\n      \n      .notification-content {\n        flex: 1;\n        min-width: 0;\n      }\n      \n      .notification-title {\n        font-weight: 600;\n        font-size: 14px;\n        margin-bottom: 4px;\n        color: #1f2937;\n      }\n      \n      .notification-message {\n        font-size: 13px;\n        color: #6b7280;\n        line-height: 1.4;\n      }\n      \n      .notification-close {\n        background: none;\n        border: none;\n        color: #9ca3af;\n        cursor: pointer;\n        padding: 0;\n        width: 20px;\n        height: 20px;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        border-radius: 4px;\n        transition: all 0.2s;\n        flex-shrink: 0;\n      }\n      \n      .notification-close:hover {\n        background-color: #f3f4f6;\n        color: #6b7280;\n      }\n      \n      @media (max-width: 640px) {\n        .notification-container {\n          left: 20px;\n          right: 20px;\n          max-width: none;\n        }\n      }\n    ",document.head.appendChild(n)}}},{key:"show",value:function(n,e,t){var r=this,a=arguments.length>3&&void 0!==arguments[3]?arguments[3]:5e3,i={id:this.nextId++,type:n,title:e,message:t,duration:a};return this.notifications.push(i),this.render(),a>0&&setTimeout(function(){r.remove(i.id)},a),i.id}},{key:"success",value:function(n,e,t){return this.show("success",n,e,t)}},{key:"error",value:function(n,e,t){return this.show("error",n,e,t)}},{key:"warning",value:function(n,e,t){return this.show("warning",n,e,t)}},{key:"info",value:function(n,e,t){return this.show("info",n,e,t)}},{key:"remove",value:function(n){var e=this.notifications.findIndex(function(e){return e.id===n});e>-1&&(this.notifications.splice(e,1),this.render())}},{key:"clear",value:function(){this.notifications=[],this.render()}},{key:"render",value:function(){var n=this;if(this.container){var e=this.container.querySelector(".notification-container");e||((e=document.createElement("div")).className="notification-container",this.container.appendChild(e)),e.innerHTML="",this.notifications.forEach(function(t){var r=n.createNotificationElement(t);e.appendChild(r),setTimeout(function(){r.classList.add("show")},10)})}}},{key:"createNotificationElement",value:function(n){var e=this,t=document.createElement("div");t.className="notification ".concat(n.type),t.dataset.id=n.id;var r=this.getIcon(n.type);return t.innerHTML='\n      <div class="notification-icon">'.concat(r,'</div>\n      <div class="notification-content">\n        <div class="notification-title">').concat(this.escapeHtml(n.title),'</div>\n        <div class="notification-message">').concat(this.escapeHtml(n.message),'</div>\n      </div>\n      <button class="notification-close" type="button">\n        <i class="fas fa-times"></i>\n      </button>\n    '),t.querySelector(".notification-close").addEventListener("click",function(){e.remove(n.id)}),t}},{key:"getIcon",value:function(n){var e={success:'<i class="fas fa-check-circle" style="color: #10b981;"></i>',error:'<i class="fas fa-exclamation-circle" style="color: #ef4444;"></i>',warning:'<i class="fas fa-exclamation-triangle" style="color: #f59e0b;"></i>',info:'<i class="fas fa-info-circle" style="color: #3b82f6;"></i>'};return e[n]||e.info}},{key:"escapeHtml",value:function(n){var e=document.createElement("div");return e.textContent=n,e.innerHTML}}])}();const I=function(){return(0,v.A)(function n(){(0,b.A)(this,n),this.authService=new L,this.notificationService=new F,this.showPassword=!1,this.isLoading=!1},[{key:"render",value:(e=(0,A.A)(w().mark(function n(e){return w().wrap(function(n){for(;;)switch(n.prev=n.next){case 0:e.innerHTML=this.getHTML(),this.bindEvents(),this.updateLoginAttempts();case 1:case"end":return n.stop()}},n,this)})),function(n){return e.apply(this,arguments)})},{key:"getHTML",value:function(){return'\n      <div class="login-container">\n        <div class="login-card">\n          <div class="login-header">\n            <h1>GG Case Catalogs</h1>\n            <h2>Admin Dashboard</h2>\n            <p>Sign in to manage your catalog</p>\n          </div>\n          \n          <form class="login-form" id="loginForm">\n            <div class="form-group">\n              <label for="username" class="form-label">Username</label>\n              <input \n                type="text" \n                id="username" \n                name="username" \n                class="form-control" \n                required \n                autocomplete="username"\n                placeholder="Enter your username"\n              >\n            </div>\n            \n            <div class="form-group">\n              <label for="password" class="form-label">Password</label>\n              <div class="password-input-container">\n                <input \n                  type="password" \n                  id="password" \n                  name="password" \n                  class="form-control password-input" \n                  required \n                  autocomplete="current-password"\n                  placeholder="Enter your password"\n                >\n                <button \n                  type="button" \n                  class="password-toggle" \n                  id="passwordToggle"\n                  title="Toggle password visibility"\n                >\n                  <i class="fas fa-eye" id="passwordToggleIcon"></i>\n                </button>\n              </div>\n            </div>\n            \n            <div class="login-attempts" id="loginAttempts"></div>\n            \n            <button \n              type="submit" \n              class="btn btn-primary btn-lg login-submit" \n              id="loginSubmit"\n            >\n              <span class="submit-text">Sign In</span>\n              <span class="submit-loading d-none">\n                <i class="fas fa-spinner fa-spin"></i>\n                Signing in...\n              </span>\n            </button>\n          </form>\n          \n          <div class="login-footer">\n            <p class="text-center">\n              <small>Secure admin access for authorized personnel only</small>\n            </p>\n          </div>\n        </div>\n      </div>\n      \n      <style>\n        .login-container {\n          min-height: 100vh;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n          padding: 2rem;\n        }\n        \n        .login-card {\n          background: white;\n          border-radius: 12px;\n          box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);\n          padding: 3rem;\n          width: 100%;\n          max-width: 400px;\n        }\n        \n        .login-header {\n          text-align: center;\n          margin-bottom: 2rem;\n        }\n        \n        .login-header h1 {\n          color: #1f2937;\n          font-size: 1.75rem;\n          margin-bottom: 0.5rem;\n        }\n        \n        .login-header h2 {\n          color: #6b7280;\n          font-size: 1.25rem;\n          font-weight: 500;\n          margin-bottom: 0.5rem;\n        }\n        \n        .login-header p {\n          color: #9ca3af;\n          font-size: 0.875rem;\n          margin-bottom: 0;\n        }\n        \n        .password-input-container {\n          position: relative;\n        }\n        \n        .password-input {\n          padding-right: 3rem;\n        }\n        \n        .password-toggle {\n          position: absolute;\n          right: 0.75rem;\n          top: 50%;\n          transform: translateY(-50%);\n          background: none;\n          border: none;\n          color: #6b7280;\n          cursor: pointer;\n          padding: 0.25rem;\n          border-radius: 0.25rem;\n          transition: color 0.2s;\n        }\n        \n        .password-toggle:hover {\n          color: #374151;\n        }\n        \n        .login-submit {\n          width: 100%;\n          margin-top: 1rem;\n        }\n        \n        .login-attempts {\n          margin-bottom: 1rem;\n          padding: 0.75rem;\n          border-radius: 0.375rem;\n          font-size: 0.875rem;\n          text-align: center;\n        }\n        \n        .login-attempts.warning {\n          background-color: #fef3c7;\n          color: #92400e;\n          border: 1px solid #fcd34d;\n        }\n        \n        .login-attempts.error {\n          background-color: #fee2e2;\n          color: #991b1b;\n          border: 1px solid #fca5a5;\n        }\n        \n        .login-attempts.hidden {\n          display: none;\n        }\n        \n        .login-footer {\n          margin-top: 2rem;\n          padding-top: 1.5rem;\n          border-top: 1px solid #e5e7eb;\n        }\n        \n        @media (max-width: 480px) {\n          .login-container {\n            padding: 1rem;\n          }\n          \n          .login-card {\n            padding: 2rem;\n          }\n        }\n      </style>\n    '}},{key:"bindEvents",value:function(){var n=this,e=document.getElementById("loginForm"),t=document.getElementById("passwordToggle"),r=document.getElementById("password"),a=document.getElementById("passwordToggleIcon");e.addEventListener("submit",function(e){e.preventDefault(),n.handleLogin()}),t.addEventListener("click",function(){n.showPassword=!n.showPassword,r.type=n.showPassword?"text":"password",a.className=n.showPassword?"fas fa-eye-slash":"fas fa-eye"}),r.addEventListener("keypress",function(e){"Enter"===e.key&&n.handleLogin()})}},{key:"handleLogin",value:(n=(0,A.A)(w().mark(function n(){var e,t,r,a,i,o,s,c;return w().wrap(function(n){for(;;)switch(n.prev=n.next){case 0:if(!this.isLoading){n.next=1;break}return n.abrupt("return");case 1:if(document.getElementById("loginForm"),e=document.getElementById("loginSubmit"),t=e.querySelector(".submit-text"),r=e.querySelector(".submit-loading"),a=document.getElementById("username"),i=document.getElementById("password"),o=a.value.trim(),s=i.value,o&&s){n.next=2;break}return this.notificationService.error("Validation Error","Please enter both username and password"),n.abrupt("return");case 2:return this.isLoading=!0,e.disabled=!0,t.classList.add("d-none"),r.classList.remove("d-none"),n.prev=3,n.next=4,this.authService.login(o,s);case 4:this.notificationService.success("Login Successful","Welcome to the admin dashboard"),setTimeout(function(){window.location.href="/admin/dashboard"},1e3),n.next=6;break;case 5:n.prev=5,c=n.catch(3),this.notificationService.error("Login Failed",c.message),this.updateLoginAttempts(),i.value="",i.focus();case 6:return n.prev=6,this.isLoading=!1,e.disabled=!1,t.classList.remove("d-none"),r.classList.add("d-none"),n.finish(6);case 7:case"end":return n.stop()}},n,this,[[3,5,6,7]])})),function(){return n.apply(this,arguments)})},{key:"updateLoginAttempts",value:function(){var n=document.getElementById("loginAttempts");if(n){var e=this.authService.getRemainingAttempts(),t=this.authService.isAccountLocked(),r=this.authService.getLockoutTimeRemaining();if(t){var a=Math.ceil(r/1e3/60);n.innerHTML='\n        <i class="fas fa-lock"></i>\n        Account locked. Try again in '.concat(a," minute").concat(1!==a?"s":"",".\n      "),n.className="login-attempts error"}else e<5&&e>0?(n.innerHTML='\n        <i class="fas fa-exclamation-triangle"></i>\n        '.concat(e," login attempt").concat(1!==e?"s":""," remaining\n      "),n.className="login-attempts warning"):n.className="login-attempts hidden"}}}]);var n,e}();const T=function(){return(0,v.A)(function n(){(0,b.A)(this,n),this.authService=new L,this.notificationService=new F,this.sidebarCollapsed=!1},[{key:"render",value:(t=(0,A.A)(w().mark(function n(e){return w().wrap(function(n){for(;;)switch(n.prev=n.next){case 0:return e.innerHTML=this.getHTML(),this.bindEvents(),n.next=1,this.loadUserInfo();case 1:case"end":return n.stop()}},n,this)})),function(n){return t.apply(this,arguments)})},{key:"getHTML",value:function(){return'\n      <div class="admin-layout">\n        <aside class="admin-sidebar" id="adminSidebar">\n          <div class="sidebar-header">\n            <div class="sidebar-logo">\n              <i class="fas fa-cube"></i>\n              <span class="sidebar-logo-text">GG Catalogs</span>\n            </div>\n            <button class="sidebar-toggle" id="sidebarToggle">\n              <i class="fas fa-bars"></i>\n            </button>\n          </div>\n          \n          <nav class="sidebar-nav">\n            <ul class="nav-list">\n              <li class="nav-item">\n                <a href="/admin/dashboard" class="nav-link" data-route="/admin/dashboard">\n                  <i class="fas fa-tachometer-alt"></i>\n                  <span class="nav-text">Dashboard</span>\n                </a>\n              </li>\n              <li class="nav-item">\n                <a href="/admin/products" class="nav-link" data-route="/admin/products">\n                  <i class="fas fa-box"></i>\n                  <span class="nav-text">Products</span>\n                </a>\n              </li>\n              <li class="nav-item">\n                <a href="/admin/brands" class="nav-link" data-route="/admin/brands">\n                  <i class="fas fa-tags"></i>\n                  <span class="nav-text">Brands</span>\n                </a>\n              </li>\n              <li class="nav-item">\n                <a href="/admin/categories" class="nav-link" data-route="/admin/categories">\n                  <i class="fas fa-list"></i>\n                  <span class="nav-text">Categories</span>\n                </a>\n              </li>\n              <li class="nav-item">\n                <a href="/admin/banners" class="nav-link" data-route="/admin/banners">\n                  <i class="fas fa-image"></i>\n                  <span class="nav-text">Banners</span>\n                </a>\n              </li>\n              <li class="nav-item">\n                <a href="/admin/admins" class="nav-link" data-route="/admin/admins">\n                  <i class="fas fa-users-cog"></i>\n                  <span class="nav-text">Admin Users</span>\n                </a>\n              </li>\n            </ul>\n          </nav>\n        </aside>\n        \n        <div class="admin-main">\n          <header class="admin-header">\n            <div class="header-left">\n              <button class="mobile-menu-toggle" id="mobileMenuToggle">\n                <i class="fas fa-bars"></i>\n              </button>\n              <h1 class="page-title" id="pageTitle">Dashboard</h1>\n            </div>\n            \n            <div class="header-right">\n              <div class="user-menu">\n                <button class="user-menu-toggle" id="userMenuToggle">\n                  <div class="user-avatar">\n                    <i class="fas fa-user"></i>\n                  </div>\n                  <span class="user-name" id="userName">Admin</span>\n                  <i class="fas fa-chevron-down"></i>\n                </button>\n                \n                <div class="user-menu-dropdown" id="userMenuDropdown">\n                  <a href="#" class="dropdown-item" id="logoutBtn">\n                    <i class="fas fa-sign-out-alt"></i>\n                    Logout\n                  </a>\n                </div>\n              </div>\n            </div>\n          </header>\n          \n          <main class="admin-content" id="adminContent">\n            \x3c!-- Page content will be loaded here --\x3e\n          </main>\n        </div>\n      </div>\n      \n      <style>\n        .admin-layout {\n          display: flex;\n          height: 100vh;\n          background-color: #f8fafc;\n        }\n        \n        .admin-sidebar {\n          width: 260px;\n          background: #1e293b;\n          color: white;\n          transition: all 0.3s ease;\n          position: relative;\n          z-index: 1000;\n        }\n        \n        .admin-sidebar.collapsed {\n          width: 70px;\n        }\n        \n        .sidebar-header {\n          padding: 1rem;\n          border-bottom: 1px solid #334155;\n          display: flex;\n          align-items: center;\n          justify-content: space-between;\n        }\n        \n        .sidebar-logo {\n          display: flex;\n          align-items: center;\n          gap: 0.75rem;\n          font-weight: 600;\n          font-size: 1.125rem;\n        }\n        \n        .sidebar-logo i {\n          font-size: 1.5rem;\n          color: #3b82f6;\n        }\n        \n        .sidebar-toggle {\n          background: none;\n          border: none;\n          color: #94a3b8;\n          cursor: pointer;\n          padding: 0.5rem;\n          border-radius: 0.375rem;\n          transition: all 0.2s;\n        }\n        \n        .sidebar-toggle:hover {\n          background-color: #334155;\n          color: white;\n        }\n        \n        .sidebar-nav {\n          padding: 1rem 0;\n        }\n        \n        .nav-list {\n          list-style: none;\n          margin: 0;\n          padding: 0;\n        }\n        \n        .nav-item {\n          margin-bottom: 0.25rem;\n        }\n        \n        .nav-link {\n          display: flex;\n          align-items: center;\n          gap: 0.75rem;\n          padding: 0.75rem 1rem;\n          color: #cbd5e1;\n          text-decoration: none;\n          transition: all 0.2s;\n          border-left: 3px solid transparent;\n        }\n        \n        .nav-link:hover {\n          background-color: #334155;\n          color: white;\n          text-decoration: none;\n        }\n        \n        .nav-link.active {\n          background-color: #1e40af;\n          color: white;\n          border-left-color: #3b82f6;\n        }\n        \n        .nav-link i {\n          width: 20px;\n          text-align: center;\n        }\n        \n        .admin-sidebar.collapsed .sidebar-logo-text,\n        .admin-sidebar.collapsed .nav-text {\n          display: none;\n        }\n        \n        .admin-main {\n          flex: 1;\n          display: flex;\n          flex-direction: column;\n          overflow: hidden;\n        }\n        \n        .admin-header {\n          background: white;\n          border-bottom: 1px solid #e2e8f0;\n          padding: 0 1.5rem;\n          height: 64px;\n          display: flex;\n          align-items: center;\n          justify-content: space-between;\n          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\n        }\n        \n        .header-left {\n          display: flex;\n          align-items: center;\n          gap: 1rem;\n        }\n        \n        .mobile-menu-toggle {\n          display: none;\n          background: none;\n          border: none;\n          color: #64748b;\n          cursor: pointer;\n          padding: 0.5rem;\n          border-radius: 0.375rem;\n        }\n        \n        .page-title {\n          font-size: 1.5rem;\n          font-weight: 600;\n          color: #1e293b;\n          margin: 0;\n        }\n        \n        .user-menu {\n          position: relative;\n        }\n        \n        .user-menu-toggle {\n          display: flex;\n          align-items: center;\n          gap: 0.5rem;\n          background: none;\n          border: none;\n          cursor: pointer;\n          padding: 0.5rem;\n          border-radius: 0.5rem;\n          transition: background-color 0.2s;\n        }\n        \n        .user-menu-toggle:hover {\n          background-color: #f1f5f9;\n        }\n        \n        .user-avatar {\n          width: 32px;\n          height: 32px;\n          background: #3b82f6;\n          border-radius: 50%;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          color: white;\n        }\n        \n        .user-name {\n          font-weight: 500;\n          color: #1e293b;\n        }\n        \n        .user-menu-dropdown {\n          position: absolute;\n          top: 100%;\n          right: 0;\n          background: white;\n          border: 1px solid #e2e8f0;\n          border-radius: 0.5rem;\n          box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);\n          min-width: 150px;\n          z-index: 1000;\n          display: none;\n        }\n        \n        .user-menu-dropdown.show {\n          display: block;\n        }\n        \n        .dropdown-item {\n          display: flex;\n          align-items: center;\n          gap: 0.5rem;\n          padding: 0.75rem 1rem;\n          color: #374151;\n          text-decoration: none;\n          transition: background-color 0.2s;\n        }\n        \n        .dropdown-item:hover {\n          background-color: #f9fafb;\n          text-decoration: none;\n          color: #374151;\n        }\n        \n        .admin-content {\n          flex: 1;\n          overflow-y: auto;\n          padding: 1.5rem;\n        }\n        \n        @media (max-width: 768px) {\n          .admin-sidebar {\n            position: fixed;\n            left: -260px;\n            height: 100vh;\n            z-index: 1001;\n          }\n          \n          .admin-sidebar.mobile-open {\n            left: 0;\n          }\n          \n          .admin-main {\n            width: 100%;\n          }\n          \n          .mobile-menu-toggle {\n            display: block;\n          }\n          \n          .admin-content {\n            padding: 1rem;\n          }\n        }\n      </style>\n    '}},{key:"bindEvents",value:function(){var n=this,e=document.getElementById("sidebarToggle"),t=document.getElementById("mobileMenuToggle"),r=document.getElementById("userMenuToggle"),a=document.getElementById("userMenuDropdown"),i=document.getElementById("logoutBtn"),o=document.getElementById("adminSidebar");null==e||e.addEventListener("click",function(){n.sidebarCollapsed=!n.sidebarCollapsed,o.classList.toggle("collapsed",n.sidebarCollapsed)}),null==t||t.addEventListener("click",function(){o.classList.toggle("mobile-open")}),null==r||r.addEventListener("click",function(n){n.stopPropagation(),a.classList.toggle("show")}),document.addEventListener("click",function(){a.classList.remove("show")}),null==i||i.addEventListener("click",function(){var e=(0,A.A)(w().mark(function e(t){return w().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return t.preventDefault(),e.next=1,n.handleLogout();case 1:case"end":return e.stop()}},e)}));return function(n){return e.apply(this,arguments)}}()),document.querySelectorAll(".nav-link").forEach(function(e){e.addEventListener("click",function(t){t.preventDefault();var r=e.dataset.route;r&&n.navigateToRoute(r)})}),this.updateActiveNavLink()}},{key:"loadUserInfo",value:(e=(0,A.A)(w().mark(function n(){var e,t;return w().wrap(function(n){for(;;)switch(n.prev=n.next){case 0:try{(e=this.authService.getUser())&&(t=document.getElementById("userName"))&&(t.textContent=e.username||"Admin")}catch(n){}case 1:case"end":return n.stop()}},n,this)})),function(){return e.apply(this,arguments)})},{key:"handleLogout",value:(n=(0,A.A)(w().mark(function n(){return w().wrap(function(n){for(;;)switch(n.prev=n.next){case 0:return n.prev=0,n.next=1,this.authService.logout();case 1:this.notificationService.success("Logged Out","You have been successfully logged out"),window.location.href="/login",n.next=3;break;case 2:n.prev=2,n.catch(0),this.notificationService.error("Logout Error","There was an error logging out");case 3:case"end":return n.stop()}},n,this,[[0,2]])})),function(){return n.apply(this,arguments)})},{key:"navigateToRoute",value:function(n){document.querySelectorAll(".nav-link").forEach(function(e){e.classList.remove("active"),e.dataset.route===n&&e.classList.add("active")});var e=document.getElementById("pageTitle");if(e){e.textContent={"/admin/dashboard":"Dashboard","/admin/products":"Products","/admin/brands":"Brands","/admin/categories":"Categories","/admin/banners":"Banners","/admin/admins":"Admin Users"}[n]||"Dashboard"}window.history&&(window.history.pushState({},"",n),window.dispatchEvent(new PopStateEvent("popstate")))}},{key:"updateActiveNavLink",value:function(){var n=window.location.pathname;document.querySelectorAll(".nav-link").forEach(function(e){e.classList.remove("active"),e.dataset.route===n&&e.classList.add("active")})}}]);var n,e,t}();const P=function(){return(0,v.A)(function n(){(0,b.A)(this,n),this.apiService=new S,this.notificationService=new F,this.stats={products:0,brands:0,categories:0,banners:0}},[{key:"render",value:(t=(0,A.A)(w().mark(function n(e){return w().wrap(function(n){for(;;)switch(n.prev=n.next){case 0:return e.innerHTML=this.getHTML(),n.next=1,this.loadStats();case 1:case"end":return n.stop()}},n,this)})),function(n){return t.apply(this,arguments)})},{key:"getHTML",value:function(){return'\n      <div class="dashboard-page">\n        <div class="dashboard-header">\n          <h1>Dashboard Overview</h1>\n          <p>Welcome to the GG Case Catalogs admin dashboard</p>\n        </div>\n        \n        <div class="stats-grid">\n          <div class="stat-card">\n            <div class="stat-icon products">\n              <i class="fas fa-box"></i>\n            </div>\n            <div class="stat-content">\n              <h3 id="productsCount">-</h3>\n              <p>Total Products</p>\n            </div>\n          </div>\n          \n          <div class="stat-card">\n            <div class="stat-icon brands">\n              <i class="fas fa-tags"></i>\n            </div>\n            <div class="stat-content">\n              <h3 id="brandsCount">-</h3>\n              <p>Total Brands</p>\n            </div>\n          </div>\n          \n          <div class="stat-card">\n            <div class="stat-icon categories">\n              <i class="fas fa-list"></i>\n            </div>\n            <div class="stat-content">\n              <h3 id="categoriesCount">-</h3>\n              <p>Total Categories</p>\n            </div>\n          </div>\n          \n          <div class="stat-card">\n            <div class="stat-icon banners">\n              <i class="fas fa-image"></i>\n            </div>\n            <div class="stat-content">\n              <h3 id="bannersCount">-</h3>\n              <p>Active Banners</p>\n            </div>\n          </div>\n        </div>\n        \n        <div class="dashboard-content">\n          <div class="dashboard-row">\n            <div class="dashboard-col">\n              <div class="card">\n                <div class="card-header">\n                  <h3>Quick Actions</h3>\n                </div>\n                <div class="card-body">\n                  <div class="quick-actions">\n                    <a href="/admin/products" class="quick-action-btn">\n                      <i class="fas fa-plus"></i>\n                      Add Product\n                    </a>\n                    <a href="/admin/brands" class="quick-action-btn">\n                      <i class="fas fa-plus"></i>\n                      Add Brand\n                    </a>\n                    <a href="/admin/categories" class="quick-action-btn">\n                      <i class="fas fa-plus"></i>\n                      Add Category\n                    </a>\n                    <a href="/admin/banners" class="quick-action-btn">\n                      <i class="fas fa-plus"></i>\n                      Add Banner\n                    </a>\n                  </div>\n                </div>\n              </div>\n            </div>\n            \n            <div class="dashboard-col">\n              <div class="card">\n                <div class="card-header">\n                  <h3>System Status</h3>\n                </div>\n                <div class="card-body">\n                  <div class="status-item">\n                    <span class="status-label">Database</span>\n                    <span class="status-indicator online" id="dbStatus">\n                      <i class="fas fa-circle"></i>\n                      Online\n                    </span>\n                  </div>\n                  <div class="status-item">\n                    <span class="status-label">API Server</span>\n                    <span class="status-indicator online">\n                      <i class="fas fa-circle"></i>\n                      Running\n                    </span>\n                  </div>\n                  <div class="status-item">\n                    <span class="status-label">File Uploads</span>\n                    <span class="status-indicator online">\n                      <i class="fas fa-circle"></i>\n                      Available\n                    </span>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n      \n      <style>\n        .dashboard-page {\n          max-width: 1200px;\n          margin: 0 auto;\n        }\n        \n        .dashboard-header {\n          margin-bottom: 2rem;\n        }\n        \n        .dashboard-header h1 {\n          font-size: 2rem;\n          color: #1e293b;\n          margin-bottom: 0.5rem;\n        }\n        \n        .dashboard-header p {\n          color: #64748b;\n          font-size: 1rem;\n        }\n        \n        .stats-grid {\n          display: grid;\n          grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\n          gap: 1.5rem;\n          margin-bottom: 2rem;\n        }\n        \n        .stat-card {\n          background: white;\n          border-radius: 0.75rem;\n          padding: 1.5rem;\n          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\n          display: flex;\n          align-items: center;\n          gap: 1rem;\n          transition: transform 0.2s, box-shadow 0.2s;\n        }\n        \n        .stat-card:hover {\n          transform: translateY(-2px);\n          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\n        }\n        \n        .stat-icon {\n          width: 60px;\n          height: 60px;\n          border-radius: 0.75rem;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          font-size: 1.5rem;\n          color: white;\n        }\n        \n        .stat-icon.products { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }\n        .stat-icon.brands { background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); }\n        .stat-icon.categories { background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); }\n        .stat-icon.banners { background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); }\n        \n        .stat-content h3 {\n          font-size: 2rem;\n          font-weight: 700;\n          color: #1e293b;\n          margin-bottom: 0.25rem;\n        }\n        \n        .stat-content p {\n          color: #64748b;\n          font-size: 0.875rem;\n          margin: 0;\n        }\n        \n        .dashboard-content {\n          margin-top: 2rem;\n        }\n        \n        .dashboard-row {\n          display: grid;\n          grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));\n          gap: 1.5rem;\n        }\n        \n        .card {\n          background: white;\n          border-radius: 0.75rem;\n          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\n          overflow: hidden;\n        }\n        \n        .card-header {\n          padding: 1.5rem 1.5rem 0;\n        }\n        \n        .card-header h3 {\n          font-size: 1.25rem;\n          font-weight: 600;\n          color: #1e293b;\n          margin: 0;\n        }\n        \n        .card-body {\n          padding: 1.5rem;\n        }\n        \n        .quick-actions {\n          display: grid;\n          grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));\n          gap: 1rem;\n        }\n        \n        .quick-action-btn {\n          display: flex;\n          flex-direction: column;\n          align-items: center;\n          gap: 0.5rem;\n          padding: 1rem;\n          background: #f8fafc;\n          border: 1px solid #e2e8f0;\n          border-radius: 0.5rem;\n          text-decoration: none;\n          color: #475569;\n          transition: all 0.2s;\n        }\n        \n        .quick-action-btn:hover {\n          background: #3b82f6;\n          color: white;\n          text-decoration: none;\n          transform: translateY(-1px);\n        }\n        \n        .quick-action-btn i {\n          font-size: 1.25rem;\n        }\n        \n        .status-item {\n          display: flex;\n          justify-content: space-between;\n          align-items: center;\n          padding: 0.75rem 0;\n          border-bottom: 1px solid #f1f5f9;\n        }\n        \n        .status-item:last-child {\n          border-bottom: none;\n        }\n        \n        .status-label {\n          font-weight: 500;\n          color: #374151;\n        }\n        \n        .status-indicator {\n          display: flex;\n          align-items: center;\n          gap: 0.5rem;\n          font-size: 0.875rem;\n          font-weight: 500;\n        }\n        \n        .status-indicator.online {\n          color: #059669;\n        }\n        \n        .status-indicator.offline {\n          color: #dc2626;\n        }\n        \n        .status-indicator i {\n          font-size: 0.5rem;\n        }\n        \n        @media (max-width: 768px) {\n          .stats-grid {\n            grid-template-columns: 1fr;\n          }\n          \n          .dashboard-row {\n            grid-template-columns: 1fr;\n          }\n          \n          .quick-actions {\n            grid-template-columns: repeat(2, 1fr);\n          }\n        }\n      </style>\n    '}},{key:"loadStats",value:(e=(0,A.A)(w().mark(function n(){var e,t,r,a,i,o,s,c,d,l;return w().wrap(function(n){for(;;)switch(n.prev=n.next){case 0:return n.prev=0,n.next=1,Promise.all([this.apiService.get("/products").catch(function(){return{data:[]}}),this.apiService.get("/brands").catch(function(){return{data:[]}}),this.apiService.get("/categories").catch(function(){return{data:[]}}),this.apiService.get("/banners/active").catch(function(){return{data:[]}})]);case 1:return i=n.sent,o=(0,y.A)(i,4),s=o[0],c=o[1],d=o[2],l=o[3],this.stats.products=(null===(e=s.data)||void 0===e?void 0:e.length)||0,this.stats.brands=(null===(t=c.data)||void 0===t?void 0:t.length)||0,this.stats.categories=(null===(r=d.data)||void 0===r?void 0:r.length)||0,this.stats.banners=(null===(a=l.data)||void 0===a?void 0:a.length)||0,this.updateStatsDisplay(),n.next=2,this.testDatabaseConnection();case 2:n.next=4;break;case 3:n.prev=3,n.catch(0),this.notificationService.error("Error","Failed to load dashboard statistics");case 4:case"end":return n.stop()}},n,this,[[0,3]])})),function(){return e.apply(this,arguments)})},{key:"updateStatsDisplay",value:function(){var n={productsCount:document.getElementById("productsCount"),brandsCount:document.getElementById("brandsCount"),categoriesCount:document.getElementById("categoriesCount"),bannersCount:document.getElementById("bannersCount")};n.productsCount&&(n.productsCount.textContent=this.stats.products),n.brandsCount&&(n.brandsCount.textContent=this.stats.brands),n.categoriesCount&&(n.categoriesCount.textContent=this.stats.categories),n.bannersCount&&(n.bannersCount.textContent=this.stats.banners)}},{key:"testDatabaseConnection",value:(n=(0,A.A)(w().mark(function n(){var e,t;return w().wrap(function(n){for(;;)switch(n.prev=n.next){case 0:return n.prev=0,n.next=1,this.apiService.get("/test-db");case 1:(e=document.getElementById("dbStatus"))&&(e.className="status-indicator online",e.innerHTML='<i class="fas fa-circle"></i> Online'),n.next=3;break;case 2:n.prev=2,n.catch(0),(t=document.getElementById("dbStatus"))&&(t.className="status-indicator offline",t.innerHTML='<i class="fas fa-circle"></i> Offline');case 3:case"end":return n.stop()}},n,this,[[0,2]])})),function(){return n.apply(this,arguments)})}]);var n,e,t}();const z=function(){return(0,v.A)(function n(){(0,b.A)(this,n),this.apiService=new S,this.notificationService=new F,this.products=[],this.brands=[],this.categories=[],this.showForm=!1,this.editingProduct=null},[{key:"render",value:(r=(0,A.A)(w().mark(function n(e){return w().wrap(function(n){for(;;)switch(n.prev=n.next){case 0:return e.innerHTML=this.getHTML(),this.bindEvents(),n.next=1,this.loadData();case 1:case"end":return n.stop()}},n,this)})),function(n){return r.apply(this,arguments)})},{key:"getHTML",value:function(){return'\n      <div class="products-page">\n        <div class="page-header">\n          <div class="header-content">\n            <h1>Products Management</h1>\n            <p>Manage your product catalog</p>\n          </div>\n          <button class="btn btn-primary" id="addProductBtn">\n            <i class="fas fa-plus"></i>\n            Add Product\n          </button>\n        </div>\n\n        <div class="products-content">\n          <div class="products-list" id="productsList">\n            <div class="loading-state">\n              <i class="fas fa-spinner fa-spin"></i>\n              Loading products...\n            </div>\n          </div>\n\n          <div class="product-form-modal" id="productFormModal" style="display: none;">\n            <div class="modal-backdrop"></div>\n            <div class="modal-content">\n              <div class="modal-header">\n                <h3 id="formTitle">Add New Product</h3>\n                <button class="modal-close" id="closeFormBtn">\n                  <i class="fas fa-times"></i>\n                </button>\n              </div>\n              <form class="product-form" id="productForm">\n                <div class="form-row">\n                  <div class="form-group">\n                    <label for="productName" class="form-label">Product Name *</label>\n                    <input type="text" id="productName" name="name" class="form-control" required>\n                  </div>\n                </div>\n                \n                <div class="form-group">\n                  <label for="productDescription" class="form-label">Description</label>\n                  <textarea id="productDescription" name="description" class="form-control" rows="4"></textarea>\n                </div>\n                \n                <div class="form-row">\n                  <div class="form-group">\n                    <label for="productBrand" class="form-label">Brand</label>\n                    <select id="productBrand" name="brand_id" class="form-control">\n                      <option value="">Select Brand</option>\n                    </select>\n                  </div>\n                  <div class="form-group">\n                    <label for="productCategory" class="form-label">Category</label>\n                    <select id="productCategory" name="category_id" class="form-control">\n                      <option value="">Select Category</option>\n                    </select>\n                  </div>\n                </div>\n\n                <div class="form-actions">\n                  <button type="button" class="btn btn-secondary" id="cancelFormBtn">Cancel</button>\n                  <button type="submit" class="btn btn-primary" id="saveProductBtn">\n                    <span class="btn-text">Save Product</span>\n                    <span class="btn-loading d-none">\n                      <i class="fas fa-spinner fa-spin"></i>\n                      Saving...\n                    </span>\n                  </button>\n                </div>\n              </form>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <style>\n        .products-page {\n          max-width: 1200px;\n          margin: 0 auto;\n        }\n\n        .page-header {\n          display: flex;\n          justify-content: space-between;\n          align-items: flex-start;\n          margin-bottom: 2rem;\n          gap: 1rem;\n        }\n\n        .header-content h1 {\n          font-size: 2rem;\n          color: #1e293b;\n          margin-bottom: 0.5rem;\n        }\n\n        .header-content p {\n          color: #64748b;\n          margin: 0;\n        }\n\n        .products-list {\n          background: white;\n          border-radius: 0.75rem;\n          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\n          overflow: hidden;\n        }\n\n        .loading-state {\n          padding: 3rem;\n          text-align: center;\n          color: #64748b;\n        }\n\n        .products-table {\n          width: 100%;\n          border-collapse: collapse;\n        }\n\n        .products-table th,\n        .products-table td {\n          padding: 1rem;\n          text-align: left;\n          border-bottom: 1px solid #f1f5f9;\n        }\n\n        .products-table th {\n          background: #f8fafc;\n          font-weight: 600;\n          color: #374151;\n        }\n\n        .products-table tr:hover {\n          background: #f9fafb;\n        }\n\n        .product-actions {\n          display: flex;\n          gap: 0.5rem;\n        }\n\n        .btn-icon {\n          padding: 0.5rem;\n          border: none;\n          border-radius: 0.375rem;\n          cursor: pointer;\n          transition: all 0.2s;\n        }\n\n        .btn-edit {\n          background: #3b82f6;\n          color: white;\n        }\n\n        .btn-edit:hover {\n          background: #2563eb;\n        }\n\n        .btn-delete {\n          background: #ef4444;\n          color: white;\n        }\n\n        .btn-delete:hover {\n          background: #dc2626;\n        }\n\n        .product-form-modal {\n          position: fixed;\n          top: 0;\n          left: 0;\n          right: 0;\n          bottom: 0;\n          z-index: 1000;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          padding: 1rem;\n        }\n\n        .modal-backdrop {\n          position: absolute;\n          top: 0;\n          left: 0;\n          right: 0;\n          bottom: 0;\n          background: rgba(0, 0, 0, 0.5);\n        }\n\n        .modal-content {\n          background: white;\n          border-radius: 0.75rem;\n          box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);\n          width: 100%;\n          max-width: 600px;\n          max-height: 90vh;\n          overflow-y: auto;\n          position: relative;\n          z-index: 1;\n        }\n\n        .modal-header {\n          display: flex;\n          justify-content: space-between;\n          align-items: center;\n          padding: 1.5rem;\n          border-bottom: 1px solid #e5e7eb;\n        }\n\n        .modal-header h3 {\n          margin: 0;\n          font-size: 1.25rem;\n          font-weight: 600;\n          color: #1e293b;\n        }\n\n        .modal-close {\n          background: none;\n          border: none;\n          color: #6b7280;\n          cursor: pointer;\n          padding: 0.5rem;\n          border-radius: 0.375rem;\n          transition: all 0.2s;\n        }\n\n        .modal-close:hover {\n          background: #f3f4f6;\n          color: #374151;\n        }\n\n        .product-form {\n          padding: 1.5rem;\n        }\n\n        .form-row {\n          display: grid;\n          grid-template-columns: 1fr 1fr;\n          gap: 1rem;\n        }\n\n        .form-actions {\n          display: flex;\n          justify-content: flex-end;\n          gap: 1rem;\n          margin-top: 2rem;\n          padding-top: 1.5rem;\n          border-top: 1px solid #e5e7eb;\n        }\n\n        .empty-state {\n          padding: 3rem;\n          text-align: center;\n          color: #64748b;\n        }\n\n        .empty-state i {\n          font-size: 3rem;\n          margin-bottom: 1rem;\n          color: #d1d5db;\n        }\n\n        @media (max-width: 768px) {\n          .page-header {\n            flex-direction: column;\n            align-items: stretch;\n          }\n\n          .form-row {\n            grid-template-columns: 1fr;\n          }\n\n          .modal-content {\n            margin: 1rem;\n            max-width: none;\n          }\n\n          .products-table {\n            font-size: 0.875rem;\n          }\n\n          .products-table th,\n          .products-table td {\n            padding: 0.75rem 0.5rem;\n          }\n        }\n      </style>\n    '}},{key:"bindEvents",value:function(){var n=this,e=document.getElementById("addProductBtn"),t=document.getElementById("closeFormBtn"),r=document.getElementById("cancelFormBtn"),a=document.getElementById("productForm"),i=document.getElementById("productFormModal"),o=null==i?void 0:i.querySelector(".modal-backdrop");null==e||e.addEventListener("click",function(){return n.showProductForm()}),null==t||t.addEventListener("click",function(){return n.hideProductForm()}),null==r||r.addEventListener("click",function(){return n.hideProductForm()}),null==o||o.addEventListener("click",function(){return n.hideProductForm()}),null==a||a.addEventListener("submit",function(e){return n.handleFormSubmit(e)})}},{key:"loadData",value:(t=(0,A.A)(w().mark(function n(){var e,t,r,a,i;return w().wrap(function(n){for(;;)switch(n.prev=n.next){case 0:return n.prev=0,n.next=1,Promise.all([this.apiService.get("/products"),this.apiService.get("/brands"),this.apiService.get("/categories")]);case 1:e=n.sent,t=(0,y.A)(e,3),r=t[0],a=t[1],i=t[2],this.products=r.data||[],this.brands=a.data||[],this.categories=i.data||[],this.renderProductsList(),this.populateFormSelects(),n.next=3;break;case 2:n.prev=2,n.catch(0),this.notificationService.error("Error","Failed to load products data"),this.renderError();case 3:case"end":return n.stop()}},n,this,[[0,2]])})),function(){return t.apply(this,arguments)})},{key:"renderProductsList",value:function(){var n=this,e=document.getElementById("productsList");if(e)if(0!==this.products.length){var t='\n      <table class="products-table">\n        <thead>\n          <tr>\n            <th>Name</th>\n            <th>Brand</th>\n            <th>Category</th>\n            <th>Created</th>\n            <th>Actions</th>\n          </tr>\n        </thead>\n        <tbody>\n          '.concat(this.products.map(function(e){return'\n            <tr>\n              <td>\n                <div>\n                  <div class="font-weight-500">'.concat(n.escapeHtml(e.name),"</div>\n                  ").concat(e.description?'<div class="text-sm text-gray-500">'.concat(n.escapeHtml(e.description.substring(0,60))).concat(e.description.length>60?"...":"","</div>"):"","\n                </div>\n              </td>\n              <td>").concat(e.brand_name||"-","</td>\n              <td>").concat(e.category_name||"-","</td>\n              <td>").concat(new Date(e.created_at).toLocaleDateString(),'</td>\n              <td>\n                <div class="product-actions">\n                  <button class="btn-icon btn-edit" onclick="window.productsPage.editProduct(').concat(e.id,')" title="Edit">\n                    <i class="fas fa-edit"></i>\n                  </button>\n                  <button class="btn-icon btn-delete" onclick="window.productsPage.deleteProduct(').concat(e.id,')" title="Delete">\n                    <i class="fas fa-trash"></i>\n                  </button>\n                </div>\n              </td>\n            </tr>\n          ')}).join(""),"\n        </tbody>\n      </table>\n    ");e.innerHTML=t,window.productsPage=this}else e.innerHTML='\n        <div class="empty-state">\n          <i class="fas fa-box"></i>\n          <h3>No Products Found</h3>\n          <p>Start by adding your first product to the catalog.</p>\n        </div>\n      '}},{key:"renderError",value:function(){var n=document.getElementById("productsList");n&&(n.innerHTML='\n        <div class="empty-state">\n          <i class="fas fa-exclamation-triangle"></i>\n          <h3>Error Loading Products</h3>\n          <p>There was an error loading the products. Please try again.</p>\n          <button class="btn btn-primary" onclick="window.location.reload()">Retry</button>\n        </div>\n      ')}},{key:"populateFormSelects",value:function(){var n=this,e=document.getElementById("productBrand"),t=document.getElementById("productCategory");e&&(e.innerHTML='<option value="">Select Brand</option>'+this.brands.map(function(e){return'<option value="'.concat(e.id,'">').concat(n.escapeHtml(e.name),"</option>")}).join("")),t&&(t.innerHTML='<option value="">Select Category</option>'+this.categories.map(function(e){return'<option value="'.concat(e.id,'">').concat(n.escapeHtml(e.name),"</option>")}).join(""))}},{key:"showProductForm",value:function(){var n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null;this.editingProduct=n;var e=document.getElementById("productFormModal"),t=document.getElementById("productForm"),r=document.getElementById("formTitle");n?(r.textContent="Edit Product",this.populateForm(n)):(r.textContent="Add New Product",t.reset()),e.style.display="flex",document.body.style.overflow="hidden"}},{key:"hideProductForm",value:function(){document.getElementById("productFormModal").style.display="none",document.body.style.overflow="",this.editingProduct=null}},{key:"populateForm",value:function(n){document.getElementById("productName").value=n.name||"",document.getElementById("productDescription").value=n.description||"",document.getElementById("productBrand").value=n.brand_id||"",document.getElementById("productCategory").value=n.category_id||""}},{key:"handleFormSubmit",value:(e=(0,A.A)(w().mark(function n(e){var t,r,a,i,o,s;return w().wrap(function(n){for(;;)switch(n.prev=n.next){case 0:if(e.preventDefault(),t=document.getElementById("saveProductBtn"),r=t.querySelector(".btn-text"),a=t.querySelector(".btn-loading"),i=new FormData(e.target),o={name:i.get("name"),description:i.get("description"),brand_id:i.get("brand_id")||null,category_id:i.get("category_id")||null},t.disabled=!0,r.classList.add("d-none"),a.classList.remove("d-none"),n.prev=1,!this.editingProduct){n.next=3;break}return n.next=2,this.apiService.put("/products/".concat(this.editingProduct.id),o);case 2:this.notificationService.success("Success","Product updated successfully"),n.next=5;break;case 3:return n.next=4,this.apiService.post("/products",o);case 4:this.notificationService.success("Success","Product created successfully");case 5:return this.hideProductForm(),n.next=6,this.loadData();case 6:n.next=8;break;case 7:n.prev=7,s=n.catch(1),this.notificationService.error("Error",s.message||"Failed to save product");case 8:return n.prev=8,t.disabled=!1,r.classList.remove("d-none"),a.classList.add("d-none"),n.finish(8);case 9:case"end":return n.stop()}},n,this,[[1,7,8,9]])})),function(n){return e.apply(this,arguments)})},{key:"editProduct",value:function(n){var e=this.products.find(function(e){return e.id===n});e&&this.showProductForm(e)}},{key:"deleteProduct",value:(n=(0,A.A)(w().mark(function n(e){var t,r;return w().wrap(function(n){for(;;)switch(n.prev=n.next){case 0:if(t=this.products.find(function(n){return n.id===e})){n.next=1;break}return n.abrupt("return");case 1:if(confirm('Are you sure you want to delete "'.concat(t.name,'"? This action cannot be undone.'))){n.next=2;break}return n.abrupt("return");case 2:return n.prev=2,n.next=3,this.apiService.delete("/products/".concat(e));case 3:return this.notificationService.success("Success","Product deleted successfully"),n.next=4,this.loadData();case 4:n.next=6;break;case 5:n.prev=5,r=n.catch(2),this.notificationService.error("Error",r.message||"Failed to delete product");case 6:case"end":return n.stop()}},n,this,[[2,5]])})),function(e){return n.apply(this,arguments)})},{key:"escapeHtml",value:function(n){var e=document.createElement("div");return e.textContent=n,e.innerHTML}}]);var n,e,t,r}();const M=function(){return(0,v.A)(function n(){(0,b.A)(this,n),this.apiService=new S,this.notificationService=new F,this.brands=[],this.showForm=!1,this.editingBrand=null},[{key:"render",value:(r=(0,A.A)(w().mark(function n(e){return w().wrap(function(n){for(;;)switch(n.prev=n.next){case 0:return e.innerHTML=this.getHTML(),this.bindEvents(),n.next=1,this.loadBrands();case 1:case"end":return n.stop()}},n,this)})),function(n){return r.apply(this,arguments)})},{key:"getHTML",value:function(){return'\n      <div class="brands-page">\n        <div class="page-header">\n          <div class="header-content">\n            <h1>Brands Management</h1>\n            <p>Manage product brands and their information</p>\n          </div>\n          <button class="btn btn-primary" id="addBrandBtn">\n            <i class="fas fa-plus"></i>\n            Add Brand\n          </button>\n        </div>\n\n        <div class="brands-content">\n          <div class="brands-grid" id="brandsGrid">\n            <div class="loading-state">\n              <i class="fas fa-spinner fa-spin"></i>\n              Loading brands...\n            </div>\n          </div>\n\n          <div class="brand-form-modal" id="brandFormModal" style="display: none;">\n            <div class="modal-backdrop"></div>\n            <div class="modal-content">\n              <div class="modal-header">\n                <h3 id="formTitle">Add New Brand</h3>\n                <button class="modal-close" id="closeFormBtn">\n                  <i class="fas fa-times"></i>\n                </button>\n              </div>\n              <form class="brand-form" id="brandForm">\n                <div class="form-group">\n                  <label for="brandName" class="form-label">Brand Name *</label>\n                  <input type="text" id="brandName" name="name" class="form-control" required>\n                </div>\n                \n                <div class="form-group">\n                  <label for="brandPhoto" class="form-label">Brand Photo URL</label>\n                  <input type="url" id="brandPhoto" name="brand_photo" class="form-control" placeholder="https://example.com/logo.jpg">\n                  <small class="form-text">Enter a URL for the brand logo/photo</small>\n                </div>\n\n                <div class="form-actions">\n                  <button type="button" class="btn btn-secondary" id="cancelFormBtn">Cancel</button>\n                  <button type="submit" class="btn btn-primary" id="saveBrandBtn">\n                    <span class="btn-text">Save Brand</span>\n                    <span class="btn-loading d-none">\n                      <i class="fas fa-spinner fa-spin"></i>\n                      Saving...\n                    </span>\n                  </button>\n                </div>\n              </form>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <style>\n        .brands-page {\n          max-width: 1200px;\n          margin: 0 auto;\n        }\n\n        .page-header {\n          display: flex;\n          justify-content: space-between;\n          align-items: flex-start;\n          margin-bottom: 2rem;\n          gap: 1rem;\n        }\n\n        .header-content h1 {\n          font-size: 2rem;\n          color: #1e293b;\n          margin-bottom: 0.5rem;\n        }\n\n        .header-content p {\n          color: #64748b;\n          margin: 0;\n        }\n\n        .brands-grid {\n          display: grid;\n          grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));\n          gap: 1.5rem;\n        }\n\n        .loading-state {\n          grid-column: 1 / -1;\n          padding: 3rem;\n          text-align: center;\n          color: #64748b;\n        }\n\n        .brand-card {\n          background: white;\n          border-radius: 0.75rem;\n          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\n          overflow: hidden;\n          transition: transform 0.2s, box-shadow 0.2s;\n        }\n\n        .brand-card:hover {\n          transform: translateY(-2px);\n          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\n        }\n\n        .brand-image {\n          height: 150px;\n          background: #f8fafc;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          border-bottom: 1px solid #e2e8f0;\n          overflow: hidden;\n        }\n\n        .brand-image img {\n          max-width: 100%;\n          max-height: 100%;\n          object-fit: contain;\n        }\n\n        .brand-image .placeholder {\n          color: #94a3b8;\n          font-size: 3rem;\n        }\n\n        .brand-content {\n          padding: 1.5rem;\n        }\n\n        .brand-name {\n          font-size: 1.25rem;\n          font-weight: 600;\n          color: #1e293b;\n          margin-bottom: 0.5rem;\n        }\n\n        .brand-meta {\n          color: #64748b;\n          font-size: 0.875rem;\n          margin-bottom: 1rem;\n        }\n\n        .brand-actions {\n          display: flex;\n          gap: 0.5rem;\n        }\n\n        .btn-icon {\n          padding: 0.5rem;\n          border: none;\n          border-radius: 0.375rem;\n          cursor: pointer;\n          transition: all 0.2s;\n          flex: 1;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          gap: 0.5rem;\n          font-size: 0.875rem;\n        }\n\n        .btn-edit {\n          background: #3b82f6;\n          color: white;\n        }\n\n        .btn-edit:hover {\n          background: #2563eb;\n        }\n\n        .btn-delete {\n          background: #ef4444;\n          color: white;\n        }\n\n        .btn-delete:hover {\n          background: #dc2626;\n        }\n\n        .brand-form-modal {\n          position: fixed;\n          top: 0;\n          left: 0;\n          right: 0;\n          bottom: 0;\n          z-index: 1000;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          padding: 1rem;\n        }\n\n        .modal-backdrop {\n          position: absolute;\n          top: 0;\n          left: 0;\n          right: 0;\n          bottom: 0;\n          background: rgba(0, 0, 0, 0.5);\n        }\n\n        .modal-content {\n          background: white;\n          border-radius: 0.75rem;\n          box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);\n          width: 100%;\n          max-width: 500px;\n          max-height: 90vh;\n          overflow-y: auto;\n          position: relative;\n          z-index: 1;\n        }\n\n        .modal-header {\n          display: flex;\n          justify-content: space-between;\n          align-items: center;\n          padding: 1.5rem;\n          border-bottom: 1px solid #e5e7eb;\n        }\n\n        .modal-header h3 {\n          margin: 0;\n          font-size: 1.25rem;\n          font-weight: 600;\n          color: #1e293b;\n        }\n\n        .modal-close {\n          background: none;\n          border: none;\n          color: #6b7280;\n          cursor: pointer;\n          padding: 0.5rem;\n          border-radius: 0.375rem;\n          transition: all 0.2s;\n        }\n\n        .modal-close:hover {\n          background: #f3f4f6;\n          color: #374151;\n        }\n\n        .brand-form {\n          padding: 1.5rem;\n        }\n\n        .form-text {\n          color: #6b7280;\n          font-size: 0.75rem;\n          margin-top: 0.25rem;\n        }\n\n        .form-actions {\n          display: flex;\n          justify-content: flex-end;\n          gap: 1rem;\n          margin-top: 2rem;\n          padding-top: 1.5rem;\n          border-top: 1px solid #e5e7eb;\n        }\n\n        .empty-state {\n          grid-column: 1 / -1;\n          padding: 3rem;\n          text-align: center;\n          color: #64748b;\n        }\n\n        .empty-state i {\n          font-size: 3rem;\n          margin-bottom: 1rem;\n          color: #d1d5db;\n        }\n\n        @media (max-width: 768px) {\n          .page-header {\n            flex-direction: column;\n            align-items: stretch;\n          }\n\n          .brands-grid {\n            grid-template-columns: 1fr;\n          }\n\n          .modal-content {\n            margin: 1rem;\n            max-width: none;\n          }\n        }\n      </style>\n    '}},{key:"bindEvents",value:function(){var n=this,e=document.getElementById("addBrandBtn"),t=document.getElementById("closeFormBtn"),r=document.getElementById("cancelFormBtn"),a=document.getElementById("brandForm"),i=document.getElementById("brandFormModal"),o=null==i?void 0:i.querySelector(".modal-backdrop");null==e||e.addEventListener("click",function(){return n.showBrandForm()}),null==t||t.addEventListener("click",function(){return n.hideBrandForm()}),null==r||r.addEventListener("click",function(){return n.hideBrandForm()}),null==o||o.addEventListener("click",function(){return n.hideBrandForm()}),null==a||a.addEventListener("submit",function(e){return n.handleFormSubmit(e)})}},{key:"loadBrands",value:(t=(0,A.A)(w().mark(function n(){var e;return w().wrap(function(n){for(;;)switch(n.prev=n.next){case 0:return n.prev=0,n.next=1,this.apiService.get("/brands");case 1:e=n.sent,this.brands=e.data||[],this.renderBrandsGrid(),n.next=3;break;case 2:n.prev=2,n.catch(0),this.notificationService.error("Error","Failed to load brands"),this.renderError();case 3:case"end":return n.stop()}},n,this,[[0,2]])})),function(){return t.apply(this,arguments)})},{key:"renderBrandsGrid",value:function(){var n=this,e=document.getElementById("brandsGrid");if(e)if(0!==this.brands.length){var t=this.brands.map(function(e){return'\n      <div class="brand-card">\n        <div class="brand-image">\n          '.concat(e.brand_photo?'<img src="'.concat(e.brand_photo,'" alt="').concat(n.escapeHtml(e.name),'" onerror="this.style.display=\'none\'; this.nextElementSibling.style.display=\'flex\';">\n             <div class="placeholder" style="display: none;"><i class="fas fa-image"></i></div>'):'<div class="placeholder"><i class="fas fa-image"></i></div>','\n        </div>\n        <div class="brand-content">\n          <h3 class="brand-name">').concat(n.escapeHtml(e.name),'</h3>\n          <div class="brand-meta">\n            Created: ').concat(new Date(e.created_at).toLocaleDateString(),'\n          </div>\n          <div class="brand-actions">\n            <button class="btn-icon btn-edit" onclick="window.brandsPage.editBrand(').concat(e.id,')">\n              <i class="fas fa-edit"></i>\n              Edit\n            </button>\n            <button class="btn-icon btn-delete" onclick="window.brandsPage.deleteBrand(').concat(e.id,')">\n              <i class="fas fa-trash"></i>\n              Delete\n            </button>\n          </div>\n        </div>\n      </div>\n    ')}).join("");e.innerHTML=t,window.brandsPage=this}else e.innerHTML='\n        <div class="empty-state">\n          <i class="fas fa-tags"></i>\n          <h3>No Brands Found</h3>\n          <p>Start by adding your first brand to organize your products.</p>\n        </div>\n      '}},{key:"renderError",value:function(){var n=document.getElementById("brandsGrid");n&&(n.innerHTML='\n        <div class="empty-state">\n          <i class="fas fa-exclamation-triangle"></i>\n          <h3>Error Loading Brands</h3>\n          <p>There was an error loading the brands. Please try again.</p>\n          <button class="btn btn-primary" onclick="window.location.reload()">Retry</button>\n        </div>\n      ')}},{key:"showBrandForm",value:function(){var n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null;this.editingBrand=n;var e=document.getElementById("brandFormModal"),t=document.getElementById("brandForm"),r=document.getElementById("formTitle");n?(r.textContent="Edit Brand",this.populateForm(n)):(r.textContent="Add New Brand",t.reset()),e.style.display="flex",document.body.style.overflow="hidden"}},{key:"hideBrandForm",value:function(){document.getElementById("brandFormModal").style.display="none",document.body.style.overflow="",this.editingBrand=null}},{key:"populateForm",value:function(n){document.getElementById("brandName").value=n.name||"",document.getElementById("brandPhoto").value=n.brand_photo||""}},{key:"handleFormSubmit",value:(e=(0,A.A)(w().mark(function n(e){var t,r,a,i,o,s;return w().wrap(function(n){for(;;)switch(n.prev=n.next){case 0:if(e.preventDefault(),t=document.getElementById("saveBrandBtn"),r=t.querySelector(".btn-text"),a=t.querySelector(".btn-loading"),i=new FormData(e.target),o={name:i.get("name"),brand_photo:i.get("brand_photo")||null},t.disabled=!0,r.classList.add("d-none"),a.classList.remove("d-none"),n.prev=1,!this.editingBrand){n.next=3;break}return n.next=2,this.apiService.put("/brands/".concat(this.editingBrand.id),o);case 2:this.notificationService.success("Success","Brand updated successfully"),n.next=5;break;case 3:return n.next=4,this.apiService.post("/brands",o);case 4:this.notificationService.success("Success","Brand created successfully");case 5:return this.hideBrandForm(),n.next=6,this.loadBrands();case 6:n.next=8;break;case 7:n.prev=7,s=n.catch(1),this.notificationService.error("Error",s.message||"Failed to save brand");case 8:return n.prev=8,t.disabled=!1,r.classList.remove("d-none"),a.classList.add("d-none"),n.finish(8);case 9:case"end":return n.stop()}},n,this,[[1,7,8,9]])})),function(n){return e.apply(this,arguments)})},{key:"editBrand",value:function(n){var e=this.brands.find(function(e){return e.id===n});e&&this.showBrandForm(e)}},{key:"deleteBrand",value:(n=(0,A.A)(w().mark(function n(e){var t,r;return w().wrap(function(n){for(;;)switch(n.prev=n.next){case 0:if(t=this.brands.find(function(n){return n.id===e})){n.next=1;break}return n.abrupt("return");case 1:if(confirm('Are you sure you want to delete "'.concat(t.name,'"? This action cannot be undone.'))){n.next=2;break}return n.abrupt("return");case 2:return n.prev=2,n.next=3,this.apiService.delete("/brands/".concat(e));case 3:return this.notificationService.success("Success","Brand deleted successfully"),n.next=4,this.loadBrands();case 4:n.next=6;break;case 5:n.prev=5,r=n.catch(2),this.notificationService.error("Error",r.message||"Failed to delete brand");case 6:case"end":return n.stop()}},n,this,[[2,5]])})),function(e){return n.apply(this,arguments)})},{key:"escapeHtml",value:function(n){var e=document.createElement("div");return e.textContent=n,e.innerHTML}}]);var n,e,t,r}();const q=function(){return(0,v.A)(function n(){(0,b.A)(this,n),this.apiService=new S,this.notificationService=new F,this.categories=[],this.showForm=!1,this.editingCategory=null},[{key:"render",value:(r=(0,A.A)(w().mark(function n(e){return w().wrap(function(n){for(;;)switch(n.prev=n.next){case 0:return e.innerHTML=this.getHTML(),this.bindEvents(),n.next=1,this.loadCategories();case 1:case"end":return n.stop()}},n,this)})),function(n){return r.apply(this,arguments)})},{key:"getHTML",value:function(){return'\n      <div class="categories-page">\n        <div class="page-header">\n          <div class="header-content">\n            <h1>Categories Management</h1>\n            <p>Organize your products with categories</p>\n          </div>\n          <button class="btn btn-primary" id="addCategoryBtn">\n            <i class="fas fa-plus"></i>\n            Add Category\n          </button>\n        </div>\n\n        <div class="categories-content">\n          <div class="categories-grid" id="categoriesGrid">\n            <div class="loading-state">\n              <i class="fas fa-spinner fa-spin"></i>\n              Loading categories...\n            </div>\n          </div>\n\n          <div class="category-form-modal" id="categoryFormModal" style="display: none;">\n            <div class="modal-backdrop"></div>\n            <div class="modal-content">\n              <div class="modal-header">\n                <h3 id="formTitle">Add New Category</h3>\n                <button class="modal-close" id="closeFormBtn">\n                  <i class="fas fa-times"></i>\n                </button>\n              </div>\n              <form class="category-form" id="categoryForm">\n                <div class="form-group">\n                  <label for="categoryName" class="form-label">Category Name *</label>\n                  <input type="text" id="categoryName" name="name" class="form-control" required>\n                </div>\n                \n                <div class="form-group">\n                  <label for="categoryPhoto" class="form-label">Category Photo URL</label>\n                  <input type="url" id="categoryPhoto" name="category_photo" class="form-control" placeholder="https://example.com/category.jpg">\n                  <small class="form-text">Enter a URL for the category image</small>\n                </div>\n\n                <div class="form-actions">\n                  <button type="button" class="btn btn-secondary" id="cancelFormBtn">Cancel</button>\n                  <button type="submit" class="btn btn-primary" id="saveCategoryBtn">\n                    <span class="btn-text">Save Category</span>\n                    <span class="btn-loading d-none">\n                      <i class="fas fa-spinner fa-spin"></i>\n                      Saving...\n                    </span>\n                  </button>\n                </div>\n              </form>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <style>\n        .categories-page {\n          max-width: 1200px;\n          margin: 0 auto;\n        }\n\n        .page-header {\n          display: flex;\n          justify-content: space-between;\n          align-items: flex-start;\n          margin-bottom: 2rem;\n          gap: 1rem;\n        }\n\n        .header-content h1 {\n          font-size: 2rem;\n          color: #1e293b;\n          margin-bottom: 0.5rem;\n        }\n\n        .header-content p {\n          color: #64748b;\n          margin: 0;\n        }\n\n        .categories-grid {\n          display: grid;\n          grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));\n          gap: 1.5rem;\n        }\n\n        .loading-state {\n          grid-column: 1 / -1;\n          padding: 3rem;\n          text-align: center;\n          color: #64748b;\n        }\n\n        .category-card {\n          background: white;\n          border-radius: 0.75rem;\n          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\n          overflow: hidden;\n          transition: transform 0.2s, box-shadow 0.2s;\n        }\n\n        .category-card:hover {\n          transform: translateY(-2px);\n          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\n        }\n\n        .category-image {\n          height: 150px;\n          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          border-bottom: 1px solid #e2e8f0;\n          overflow: hidden;\n          position: relative;\n        }\n\n        .category-image img {\n          max-width: 100%;\n          max-height: 100%;\n          object-fit: cover;\n          width: 100%;\n          height: 100%;\n        }\n\n        .category-image .placeholder {\n          color: white;\n          font-size: 3rem;\n          text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);\n        }\n\n        .category-content {\n          padding: 1.5rem;\n        }\n\n        .category-name {\n          font-size: 1.25rem;\n          font-weight: 600;\n          color: #1e293b;\n          margin-bottom: 0.5rem;\n        }\n\n        .category-meta {\n          color: #64748b;\n          font-size: 0.875rem;\n          margin-bottom: 1rem;\n        }\n\n        .category-actions {\n          display: flex;\n          gap: 0.5rem;\n        }\n\n        .btn-icon {\n          padding: 0.5rem;\n          border: none;\n          border-radius: 0.375rem;\n          cursor: pointer;\n          transition: all 0.2s;\n          flex: 1;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          gap: 0.5rem;\n          font-size: 0.875rem;\n        }\n\n        .btn-edit {\n          background: #3b82f6;\n          color: white;\n        }\n\n        .btn-edit:hover {\n          background: #2563eb;\n        }\n\n        .btn-delete {\n          background: #ef4444;\n          color: white;\n        }\n\n        .btn-delete:hover {\n          background: #dc2626;\n        }\n\n        .category-form-modal {\n          position: fixed;\n          top: 0;\n          left: 0;\n          right: 0;\n          bottom: 0;\n          z-index: 1000;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          padding: 1rem;\n        }\n\n        .modal-backdrop {\n          position: absolute;\n          top: 0;\n          left: 0;\n          right: 0;\n          bottom: 0;\n          background: rgba(0, 0, 0, 0.5);\n        }\n\n        .modal-content {\n          background: white;\n          border-radius: 0.75rem;\n          box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);\n          width: 100%;\n          max-width: 500px;\n          max-height: 90vh;\n          overflow-y: auto;\n          position: relative;\n          z-index: 1;\n        }\n\n        .modal-header {\n          display: flex;\n          justify-content: space-between;\n          align-items: center;\n          padding: 1.5rem;\n          border-bottom: 1px solid #e5e7eb;\n        }\n\n        .modal-header h3 {\n          margin: 0;\n          font-size: 1.25rem;\n          font-weight: 600;\n          color: #1e293b;\n        }\n\n        .modal-close {\n          background: none;\n          border: none;\n          color: #6b7280;\n          cursor: pointer;\n          padding: 0.5rem;\n          border-radius: 0.375rem;\n          transition: all 0.2s;\n        }\n\n        .modal-close:hover {\n          background: #f3f4f6;\n          color: #374151;\n        }\n\n        .category-form {\n          padding: 1.5rem;\n        }\n\n        .form-text {\n          color: #6b7280;\n          font-size: 0.75rem;\n          margin-top: 0.25rem;\n        }\n\n        .form-actions {\n          display: flex;\n          justify-content: flex-end;\n          gap: 1rem;\n          margin-top: 2rem;\n          padding-top: 1.5rem;\n          border-top: 1px solid #e5e7eb;\n        }\n\n        .empty-state {\n          grid-column: 1 / -1;\n          padding: 3rem;\n          text-align: center;\n          color: #64748b;\n        }\n\n        .empty-state i {\n          font-size: 3rem;\n          margin-bottom: 1rem;\n          color: #d1d5db;\n        }\n\n        @media (max-width: 768px) {\n          .page-header {\n            flex-direction: column;\n            align-items: stretch;\n          }\n\n          .categories-grid {\n            grid-template-columns: 1fr;\n          }\n\n          .modal-content {\n            margin: 1rem;\n            max-width: none;\n          }\n        }\n      </style>\n    '}},{key:"bindEvents",value:function(){var n=this,e=document.getElementById("addCategoryBtn"),t=document.getElementById("closeFormBtn"),r=document.getElementById("cancelFormBtn"),a=document.getElementById("categoryForm"),i=document.getElementById("categoryFormModal"),o=null==i?void 0:i.querySelector(".modal-backdrop");null==e||e.addEventListener("click",function(){return n.showCategoryForm()}),null==t||t.addEventListener("click",function(){return n.hideCategoryForm()}),null==r||r.addEventListener("click",function(){return n.hideCategoryForm()}),null==o||o.addEventListener("click",function(){return n.hideCategoryForm()}),null==a||a.addEventListener("submit",function(e){return n.handleFormSubmit(e)})}},{key:"loadCategories",value:(t=(0,A.A)(w().mark(function n(){var e;return w().wrap(function(n){for(;;)switch(n.prev=n.next){case 0:return n.prev=0,n.next=1,this.apiService.get("/categories");case 1:e=n.sent,this.categories=e.data||[],this.renderCategoriesGrid(),n.next=3;break;case 2:n.prev=2,n.catch(0),this.notificationService.error("Error","Failed to load categories"),this.renderError();case 3:case"end":return n.stop()}},n,this,[[0,2]])})),function(){return t.apply(this,arguments)})},{key:"renderCategoriesGrid",value:function(){var n=this,e=document.getElementById("categoriesGrid");if(e)if(0!==this.categories.length){var t=this.categories.map(function(e){return'\n      <div class="category-card">\n        <div class="category-image">\n          '.concat(e.category_photo?'<img src="'.concat(e.category_photo,'" alt="').concat(n.escapeHtml(e.name),'" onerror="this.style.display=\'none\'; this.nextElementSibling.style.display=\'flex\';">\n             <div class="placeholder" style="display: none;"><i class="fas fa-folder"></i></div>'):'<div class="placeholder"><i class="fas fa-folder"></i></div>','\n        </div>\n        <div class="category-content">\n          <h3 class="category-name">').concat(n.escapeHtml(e.name),'</h3>\n          <div class="category-meta">\n            Created: ').concat(new Date(e.created_at).toLocaleDateString(),'\n          </div>\n          <div class="category-actions">\n            <button class="btn-icon btn-edit" onclick="window.categoriesPage.editCategory(').concat(e.id,')">\n              <i class="fas fa-edit"></i>\n              Edit\n            </button>\n            <button class="btn-icon btn-delete" onclick="window.categoriesPage.deleteCategory(').concat(e.id,')">\n              <i class="fas fa-trash"></i>\n              Delete\n            </button>\n          </div>\n        </div>\n      </div>\n    ')}).join("");e.innerHTML=t,window.categoriesPage=this}else e.innerHTML='\n        <div class="empty-state">\n          <i class="fas fa-list"></i>\n          <h3>No Categories Found</h3>\n          <p>Start by adding your first category to organize your products.</p>\n        </div>\n      '}},{key:"renderError",value:function(){var n=document.getElementById("categoriesGrid");n&&(n.innerHTML='\n        <div class="empty-state">\n          <i class="fas fa-exclamation-triangle"></i>\n          <h3>Error Loading Categories</h3>\n          <p>There was an error loading the categories. Please try again.</p>\n          <button class="btn btn-primary" onclick="window.location.reload()">Retry</button>\n        </div>\n      ')}},{key:"showCategoryForm",value:function(){var n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null;this.editingCategory=n;var e=document.getElementById("categoryFormModal"),t=document.getElementById("categoryForm"),r=document.getElementById("formTitle");n?(r.textContent="Edit Category",this.populateForm(n)):(r.textContent="Add New Category",t.reset()),e.style.display="flex",document.body.style.overflow="hidden"}},{key:"hideCategoryForm",value:function(){document.getElementById("categoryFormModal").style.display="none",document.body.style.overflow="",this.editingCategory=null}},{key:"populateForm",value:function(n){document.getElementById("categoryName").value=n.name||"",document.getElementById("categoryPhoto").value=n.category_photo||""}},{key:"handleFormSubmit",value:(e=(0,A.A)(w().mark(function n(e){var t,r,a,i,o,s;return w().wrap(function(n){for(;;)switch(n.prev=n.next){case 0:if(e.preventDefault(),t=document.getElementById("saveCategoryBtn"),r=t.querySelector(".btn-text"),a=t.querySelector(".btn-loading"),i=new FormData(e.target),o={name:i.get("name"),category_photo:i.get("category_photo")||null},t.disabled=!0,r.classList.add("d-none"),a.classList.remove("d-none"),n.prev=1,!this.editingCategory){n.next=3;break}return n.next=2,this.apiService.put("/categories/".concat(this.editingCategory.id),o);case 2:this.notificationService.success("Success","Category updated successfully"),n.next=5;break;case 3:return n.next=4,this.apiService.post("/categories",o);case 4:this.notificationService.success("Success","Category created successfully");case 5:return this.hideCategoryForm(),n.next=6,this.loadCategories();case 6:n.next=8;break;case 7:n.prev=7,s=n.catch(1),this.notificationService.error("Error",s.message||"Failed to save category");case 8:return n.prev=8,t.disabled=!1,r.classList.remove("d-none"),a.classList.add("d-none"),n.finish(8);case 9:case"end":return n.stop()}},n,this,[[1,7,8,9]])})),function(n){return e.apply(this,arguments)})},{key:"editCategory",value:function(n){var e=this.categories.find(function(e){return e.id===n});e&&this.showCategoryForm(e)}},{key:"deleteCategory",value:(n=(0,A.A)(w().mark(function n(e){var t,r;return w().wrap(function(n){for(;;)switch(n.prev=n.next){case 0:if(t=this.categories.find(function(n){return n.id===e})){n.next=1;break}return n.abrupt("return");case 1:if(confirm('Are you sure you want to delete "'.concat(t.name,'"? This action cannot be undone.'))){n.next=2;break}return n.abrupt("return");case 2:return n.prev=2,n.next=3,this.apiService.delete("/categories/".concat(e));case 3:return this.notificationService.success("Success","Category deleted successfully"),n.next=4,this.loadCategories();case 4:n.next=6;break;case 5:n.prev=5,r=n.catch(2),this.notificationService.error("Error",r.message||"Failed to delete category");case 6:case"end":return n.stop()}},n,this,[[2,5]])})),function(e){return n.apply(this,arguments)})},{key:"escapeHtml",value:function(n){var e=document.createElement("div");return e.textContent=n,e.innerHTML}}]);var n,e,t,r}();function j(n,e){var t=Object.keys(n);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(n);e&&(r=r.filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})),t.push.apply(t,r)}return t}function H(n){for(var e=1;e<arguments.length;e++){var t=null!=arguments[e]?arguments[e]:{};e%2?j(Object(t),!0).forEach(function(e){(0,B.A)(n,e,t[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(n,Object.getOwnPropertyDescriptors(t)):j(Object(t)).forEach(function(e){Object.defineProperty(n,e,Object.getOwnPropertyDescriptor(t,e))})}return n}const D=function(){return(0,v.A)(function n(){(0,b.A)(this,n),this.apiService=new S,this.notificationService=new F,this.banners=[],this.showForm=!1,this.editingBanner=null},[{key:"render",value:(a=(0,A.A)(w().mark(function n(e){return w().wrap(function(n){for(;;)switch(n.prev=n.next){case 0:return e.innerHTML=this.getHTML(),this.bindEvents(),n.next=1,this.loadBanners();case 1:case"end":return n.stop()}},n,this)})),function(n){return a.apply(this,arguments)})},{key:"getHTML",value:function(){return'\n      <div class="banners-page">\n        <div class="page-header">\n          <div class="header-content">\n            <h1>Banners Management</h1>\n            <p>Manage website banners and promotional content</p>\n          </div>\n          <button class="btn btn-primary" id="addBannerBtn">\n            <i class="fas fa-plus"></i>\n            Add Banner\n          </button>\n        </div>\n\n        <div class="banners-content">\n          <div class="banners-list" id="bannersList">\n            <div class="loading-state">\n              <i class="fas fa-spinner fa-spin"></i>\n              Loading banners...\n            </div>\n          </div>\n\n          <div class="banner-form-modal" id="bannerFormModal" style="display: none;">\n            <div class="modal-backdrop"></div>\n            <div class="modal-content">\n              <div class="modal-header">\n                <h3 id="formTitle">Add New Banner</h3>\n                <button class="modal-close" id="closeFormBtn">\n                  <i class="fas fa-times"></i>\n                </button>\n              </div>\n              <form class="banner-form" id="bannerForm">\n                <div class="form-group">\n                  <label for="bannerTitle" class="form-label">Banner Title *</label>\n                  <input type="text" id="bannerTitle" name="title" class="form-control" required>\n                </div>\n                \n                <div class="form-group">\n                  <label for="bannerImage" class="form-label">Banner Image URL *</label>\n                  <input type="url" id="bannerImage" name="banner_image_url" class="form-control" required placeholder="https://example.com/banner.jpg">\n                  <small class="form-text">Enter a URL for the banner image</small>\n                </div>\n                \n                <div class="form-group">\n                  <label for="bannerRedirect" class="form-label">Redirect URL</label>\n                  <input type="url" id="bannerRedirect" name="redirect_url" class="form-control" placeholder="https://example.com/page">\n                  <small class="form-text">URL to redirect when banner is clicked (optional)</small>\n                </div>\n                \n                <div class="form-group">\n                  <label class="form-label">\n                    <input type="checkbox" id="bannerActive" name="active" checked>\n                    Active Banner\n                  </label>\n                  <small class="form-text">Only active banners will be displayed on the website</small>\n                </div>\n\n                <div class="form-actions">\n                  <button type="button" class="btn btn-secondary" id="cancelFormBtn">Cancel</button>\n                  <button type="submit" class="btn btn-primary" id="saveBannerBtn">\n                    <span class="btn-text">Save Banner</span>\n                    <span class="btn-loading d-none">\n                      <i class="fas fa-spinner fa-spin"></i>\n                      Saving...\n                    </span>\n                  </button>\n                </div>\n              </form>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <style>\n        .banners-page {\n          max-width: 1200px;\n          margin: 0 auto;\n        }\n\n        .page-header {\n          display: flex;\n          justify-content: space-between;\n          align-items: flex-start;\n          margin-bottom: 2rem;\n          gap: 1rem;\n        }\n\n        .header-content h1 {\n          font-size: 2rem;\n          color: #1e293b;\n          margin-bottom: 0.5rem;\n        }\n\n        .header-content p {\n          color: #64748b;\n          margin: 0;\n        }\n\n        .banners-list {\n          display: grid;\n          gap: 1.5rem;\n        }\n\n        .loading-state {\n          padding: 3rem;\n          text-align: center;\n          color: #64748b;\n        }\n\n        .banner-card {\n          background: white;\n          border-radius: 0.75rem;\n          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\n          overflow: hidden;\n          transition: transform 0.2s, box-shadow 0.2s;\n        }\n\n        .banner-card:hover {\n          transform: translateY(-2px);\n          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\n        }\n\n        .banner-preview {\n          height: 200px;\n          background: #f8fafc;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          overflow: hidden;\n          position: relative;\n        }\n\n        .banner-preview img {\n          max-width: 100%;\n          max-height: 100%;\n          object-fit: cover;\n          width: 100%;\n          height: 100%;\n        }\n\n        .banner-preview .placeholder {\n          color: #94a3b8;\n          font-size: 3rem;\n        }\n\n        .banner-status {\n          position: absolute;\n          top: 1rem;\n          right: 1rem;\n          padding: 0.25rem 0.75rem;\n          border-radius: 9999px;\n          font-size: 0.75rem;\n          font-weight: 500;\n        }\n\n        .banner-status.active {\n          background: #dcfce7;\n          color: #166534;\n        }\n\n        .banner-status.inactive {\n          background: #fee2e2;\n          color: #991b1b;\n        }\n\n        .banner-content {\n          padding: 1.5rem;\n        }\n\n        .banner-title {\n          font-size: 1.25rem;\n          font-weight: 600;\n          color: #1e293b;\n          margin-bottom: 0.5rem;\n        }\n\n        .banner-meta {\n          color: #64748b;\n          font-size: 0.875rem;\n          margin-bottom: 1rem;\n        }\n\n        .banner-url {\n          color: #3b82f6;\n          font-size: 0.875rem;\n          text-decoration: none;\n          margin-bottom: 1rem;\n          display: block;\n          word-break: break-all;\n        }\n\n        .banner-url:hover {\n          text-decoration: underline;\n        }\n\n        .banner-actions {\n          display: flex;\n          gap: 0.5rem;\n        }\n\n        .btn-icon {\n          padding: 0.5rem 1rem;\n          border: none;\n          border-radius: 0.375rem;\n          cursor: pointer;\n          transition: all 0.2s;\n          display: flex;\n          align-items: center;\n          gap: 0.5rem;\n          font-size: 0.875rem;\n        }\n\n        .btn-edit {\n          background: #3b82f6;\n          color: white;\n        }\n\n        .btn-edit:hover {\n          background: #2563eb;\n        }\n\n        .btn-toggle {\n          background: #f59e0b;\n          color: white;\n        }\n\n        .btn-toggle:hover {\n          background: #d97706;\n        }\n\n        .btn-delete {\n          background: #ef4444;\n          color: white;\n        }\n\n        .btn-delete:hover {\n          background: #dc2626;\n        }\n\n        .banner-form-modal {\n          position: fixed;\n          top: 0;\n          left: 0;\n          right: 0;\n          bottom: 0;\n          z-index: 1000;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          padding: 1rem;\n        }\n\n        .modal-backdrop {\n          position: absolute;\n          top: 0;\n          left: 0;\n          right: 0;\n          bottom: 0;\n          background: rgba(0, 0, 0, 0.5);\n        }\n\n        .modal-content {\n          background: white;\n          border-radius: 0.75rem;\n          box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);\n          width: 100%;\n          max-width: 600px;\n          max-height: 90vh;\n          overflow-y: auto;\n          position: relative;\n          z-index: 1;\n        }\n\n        .modal-header {\n          display: flex;\n          justify-content: space-between;\n          align-items: center;\n          padding: 1.5rem;\n          border-bottom: 1px solid #e5e7eb;\n        }\n\n        .modal-header h3 {\n          margin: 0;\n          font-size: 1.25rem;\n          font-weight: 600;\n          color: #1e293b;\n        }\n\n        .modal-close {\n          background: none;\n          border: none;\n          color: #6b7280;\n          cursor: pointer;\n          padding: 0.5rem;\n          border-radius: 0.375rem;\n          transition: all 0.2s;\n        }\n\n        .modal-close:hover {\n          background: #f3f4f6;\n          color: #374151;\n        }\n\n        .banner-form {\n          padding: 1.5rem;\n        }\n\n        .form-text {\n          color: #6b7280;\n          font-size: 0.75rem;\n          margin-top: 0.25rem;\n        }\n\n        .form-actions {\n          display: flex;\n          justify-content: flex-end;\n          gap: 1rem;\n          margin-top: 2rem;\n          padding-top: 1.5rem;\n          border-top: 1px solid #e5e7eb;\n        }\n\n        .empty-state {\n          padding: 3rem;\n          text-align: center;\n          color: #64748b;\n        }\n\n        .empty-state i {\n          font-size: 3rem;\n          margin-bottom: 1rem;\n          color: #d1d5db;\n        }\n\n        @media (max-width: 768px) {\n          .page-header {\n            flex-direction: column;\n            align-items: stretch;\n          }\n\n          .modal-content {\n            margin: 1rem;\n            max-width: none;\n          }\n\n          .banner-actions {\n            flex-wrap: wrap;\n          }\n\n          .btn-icon {\n            flex: 1;\n            min-width: 120px;\n          }\n        }\n      </style>\n    '}},{key:"bindEvents",value:function(){var n=this,e=document.getElementById("addBannerBtn"),t=document.getElementById("closeFormBtn"),r=document.getElementById("cancelFormBtn"),a=document.getElementById("bannerForm"),i=document.getElementById("bannerFormModal"),o=null==i?void 0:i.querySelector(".modal-backdrop");null==e||e.addEventListener("click",function(){return n.showBannerForm()}),null==t||t.addEventListener("click",function(){return n.hideBannerForm()}),null==r||r.addEventListener("click",function(){return n.hideBannerForm()}),null==o||o.addEventListener("click",function(){return n.hideBannerForm()}),null==a||a.addEventListener("submit",function(e){return n.handleFormSubmit(e)})}},{key:"loadBanners",value:(r=(0,A.A)(w().mark(function n(){var e;return w().wrap(function(n){for(;;)switch(n.prev=n.next){case 0:return n.prev=0,n.next=1,this.apiService.get("/banners");case 1:e=n.sent,this.banners=e.data||[],this.renderBannersList(),n.next=3;break;case 2:n.prev=2,n.catch(0),this.notificationService.error("Error","Failed to load banners"),this.renderError();case 3:case"end":return n.stop()}},n,this,[[0,2]])})),function(){return r.apply(this,arguments)})},{key:"renderBannersList",value:function(){var n=this,e=document.getElementById("bannersList");if(e)if(0!==this.banners.length){var t=this.banners.map(function(e){return'\n      <div class="banner-card">\n        <div class="banner-preview">\n          '.concat(e.banner_image_url?'<img src="'.concat(e.banner_image_url,'" alt="').concat(n.escapeHtml(e.title),'" onerror="this.style.display=\'none\'; this.nextElementSibling.style.display=\'flex\';">\n             <div class="placeholder" style="display: none;"><i class="fas fa-image"></i></div>'):'<div class="placeholder"><i class="fas fa-image"></i></div>','\n          <div class="banner-status ').concat(e.active?"active":"inactive",'">\n            ').concat(e.active?"Active":"Inactive",'\n          </div>\n        </div>\n        <div class="banner-content">\n          <h3 class="banner-title">').concat(n.escapeHtml(e.title),'</h3>\n          <div class="banner-meta">\n            Created: ').concat(new Date(e.created_at).toLocaleDateString(),"\n          </div>\n          ").concat(e.redirect_url?'<a href="'.concat(e.redirect_url,'" target="_blank" class="banner-url">').concat(e.redirect_url,"</a>"):"",'\n          <div class="banner-actions">\n            <button class="btn-icon btn-edit" onclick="window.bannersPage.editBanner(').concat(e.id,')">\n              <i class="fas fa-edit"></i>\n              Edit\n            </button>\n            <button class="btn-icon btn-toggle" onclick="window.bannersPage.toggleBanner(').concat(e.id,')">\n              <i class="fas fa-').concat(e.active?"eye-slash":"eye",'"></i>\n              ').concat(e.active?"Deactivate":"Activate",'\n            </button>\n            <button class="btn-icon btn-delete" onclick="window.bannersPage.deleteBanner(').concat(e.id,')">\n              <i class="fas fa-trash"></i>\n              Delete\n            </button>\n          </div>\n        </div>\n      </div>\n    ')}).join("");e.innerHTML=t,window.bannersPage=this}else e.innerHTML='\n        <div class="empty-state">\n          <i class="fas fa-image"></i>\n          <h3>No Banners Found</h3>\n          <p>Start by adding your first banner to promote content on your website.</p>\n        </div>\n      '}},{key:"renderError",value:function(){var n=document.getElementById("bannersList");n&&(n.innerHTML='\n        <div class="empty-state">\n          <i class="fas fa-exclamation-triangle"></i>\n          <h3>Error Loading Banners</h3>\n          <p>There was an error loading the banners. Please try again.</p>\n          <button class="btn btn-primary" onclick="window.location.reload()">Retry</button>\n        </div>\n      ')}},{key:"showBannerForm",value:function(){var n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null;this.editingBanner=n;var e=document.getElementById("bannerFormModal"),t=document.getElementById("bannerForm"),r=document.getElementById("formTitle");n?(r.textContent="Edit Banner",this.populateForm(n)):(r.textContent="Add New Banner",t.reset(),document.getElementById("bannerActive").checked=!0),e.style.display="flex",document.body.style.overflow="hidden"}},{key:"hideBannerForm",value:function(){document.getElementById("bannerFormModal").style.display="none",document.body.style.overflow="",this.editingBanner=null}},{key:"populateForm",value:function(n){document.getElementById("bannerTitle").value=n.title||"",document.getElementById("bannerImage").value=n.banner_image_url||"",document.getElementById("bannerRedirect").value=n.redirect_url||"",document.getElementById("bannerActive").checked=n.active}},{key:"handleFormSubmit",value:(t=(0,A.A)(w().mark(function n(e){var t,r,a,i,o,s;return w().wrap(function(n){for(;;)switch(n.prev=n.next){case 0:if(e.preventDefault(),t=document.getElementById("saveBannerBtn"),r=t.querySelector(".btn-text"),a=t.querySelector(".btn-loading"),i=new FormData(e.target),o={title:i.get("title"),banner_image_url:i.get("banner_image_url"),redirect_url:i.get("redirect_url")||null,active:i.has("active")},t.disabled=!0,r.classList.add("d-none"),a.classList.remove("d-none"),n.prev=1,!this.editingBanner){n.next=3;break}return n.next=2,this.apiService.put("/banners/".concat(this.editingBanner.id),o);case 2:this.notificationService.success("Success","Banner updated successfully"),n.next=5;break;case 3:return n.next=4,this.apiService.post("/banners",o);case 4:this.notificationService.success("Success","Banner created successfully");case 5:return this.hideBannerForm(),n.next=6,this.loadBanners();case 6:n.next=8;break;case 7:n.prev=7,s=n.catch(1),this.notificationService.error("Error",s.message||"Failed to save banner");case 8:return n.prev=8,t.disabled=!1,r.classList.remove("d-none"),a.classList.add("d-none"),n.finish(8);case 9:case"end":return n.stop()}},n,this,[[1,7,8,9]])})),function(n){return t.apply(this,arguments)})},{key:"editBanner",value:function(n){var e=this.banners.find(function(e){return e.id===n});e&&this.showBannerForm(e)}},{key:"toggleBanner",value:(e=(0,A.A)(w().mark(function n(e){var t,r;return w().wrap(function(n){for(;;)switch(n.prev=n.next){case 0:if(t=this.banners.find(function(n){return n.id===e})){n.next=1;break}return n.abrupt("return");case 1:return n.prev=1,n.next=2,this.apiService.put("/banners/".concat(e),H(H({},t),{},{active:!t.active}));case 2:return this.notificationService.success("Success","Banner ".concat(t.active?"deactivated":"activated"," successfully")),n.next=3,this.loadBanners();case 3:n.next=5;break;case 4:n.prev=4,r=n.catch(1),this.notificationService.error("Error",r.message||"Failed to toggle banner status");case 5:case"end":return n.stop()}},n,this,[[1,4]])})),function(n){return e.apply(this,arguments)})},{key:"deleteBanner",value:(n=(0,A.A)(w().mark(function n(e){var t,r;return w().wrap(function(n){for(;;)switch(n.prev=n.next){case 0:if(t=this.banners.find(function(n){return n.id===e})){n.next=1;break}return n.abrupt("return");case 1:if(confirm('Are you sure you want to delete "'.concat(t.title,'"? This action cannot be undone.'))){n.next=2;break}return n.abrupt("return");case 2:return n.prev=2,n.next=3,this.apiService.delete("/banners/".concat(e));case 3:return this.notificationService.success("Success","Banner deleted successfully"),n.next=4,this.loadBanners();case 4:n.next=6;break;case 5:n.prev=5,r=n.catch(2),this.notificationService.error("Error",r.message||"Failed to delete banner");case 6:case"end":return n.stop()}},n,this,[[2,5]])})),function(e){return n.apply(this,arguments)})},{key:"escapeHtml",value:function(n){var e=document.createElement("div");return e.textContent=n,e.innerHTML}}]);var n,e,t,r,a}();const U=function(){return(0,v.A)(function n(){(0,b.A)(this,n),this.apiService=new S,this.notificationService=new F,this.admins=[],this.showForm=!1,this.editingAdmin=null},[{key:"render",value:(r=(0,A.A)(w().mark(function n(e){return w().wrap(function(n){for(;;)switch(n.prev=n.next){case 0:return e.innerHTML=this.getHTML(),this.bindEvents(),n.next=1,this.loadAdmins();case 1:case"end":return n.stop()}},n,this)})),function(n){return r.apply(this,arguments)})},{key:"getHTML",value:function(){return'\n      <div class="admin-management-page">\n        <div class="page-header">\n          <div class="header-content">\n            <h1>Admin Users Management</h1>\n            <p>Manage administrator accounts and permissions</p>\n          </div>\n          <button class="btn btn-primary" id="addAdminBtn">\n            <i class="fas fa-plus"></i>\n            Add Admin User\n          </button>\n        </div>\n\n        <div class="admin-content">\n          <div class="admin-list" id="adminList">\n            <div class="loading-state">\n              <i class="fas fa-spinner fa-spin"></i>\n              Loading admin users...\n            </div>\n          </div>\n\n          <div class="admin-form-modal" id="adminFormModal" style="display: none;">\n            <div class="modal-backdrop"></div>\n            <div class="modal-content">\n              <div class="modal-header">\n                <h3 id="formTitle">Add New Admin User</h3>\n                <button class="modal-close" id="closeFormBtn">\n                  <i class="fas fa-times"></i>\n                </button>\n              </div>\n              <form class="admin-form" id="adminForm">\n                <div class="form-group">\n                  <label for="adminUsername" class="form-label">Username *</label>\n                  <input type="text" id="adminUsername" name="username" class="form-control" required>\n                  <small class="form-text">Username must be unique and contain only letters, numbers, and underscores</small>\n                </div>\n                \n                <div class="form-group">\n                  <label for="adminPassword" class="form-label">Password *</label>\n                  <div class="password-input-container">\n                    <input type="password" id="adminPassword" name="password" class="form-control password-input" required>\n                    <button type="button" class="password-toggle" id="passwordToggle">\n                      <i class="fas fa-eye" id="passwordToggleIcon"></i>\n                    </button>\n                  </div>\n                  <small class="form-text">Password must be at least 8 characters long</small>\n                </div>\n                \n                <div class="form-group">\n                  <label for="adminPasswordConfirm" class="form-label">Confirm Password *</label>\n                  <input type="password" id="adminPasswordConfirm" name="passwordConfirm" class="form-control" required>\n                </div>\n\n                <div class="form-actions">\n                  <button type="button" class="btn btn-secondary" id="cancelFormBtn">Cancel</button>\n                  <button type="submit" class="btn btn-primary" id="saveAdminBtn">\n                    <span class="btn-text">Save Admin User</span>\n                    <span class="btn-loading d-none">\n                      <i class="fas fa-spinner fa-spin"></i>\n                      Saving...\n                    </span>\n                  </button>\n                </div>\n              </form>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <style>\n        .admin-management-page {\n          max-width: 1200px;\n          margin: 0 auto;\n        }\n\n        .page-header {\n          display: flex;\n          justify-content: space-between;\n          align-items: flex-start;\n          margin-bottom: 2rem;\n          gap: 1rem;\n        }\n\n        .header-content h1 {\n          font-size: 2rem;\n          color: #1e293b;\n          margin-bottom: 0.5rem;\n        }\n\n        .header-content p {\n          color: #64748b;\n          margin: 0;\n        }\n\n        .admin-list {\n          background: white;\n          border-radius: 0.75rem;\n          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\n          overflow: hidden;\n        }\n\n        .loading-state {\n          padding: 3rem;\n          text-align: center;\n          color: #64748b;\n        }\n\n        .admin-table {\n          width: 100%;\n          border-collapse: collapse;\n        }\n\n        .admin-table th,\n        .admin-table td {\n          padding: 1rem;\n          text-align: left;\n          border-bottom: 1px solid #f1f5f9;\n        }\n\n        .admin-table th {\n          background: #f8fafc;\n          font-weight: 600;\n          color: #374151;\n        }\n\n        .admin-table tr:hover {\n          background: #f9fafb;\n        }\n\n        .admin-avatar {\n          width: 40px;\n          height: 40px;\n          background: #3b82f6;\n          border-radius: 50%;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          color: white;\n          font-weight: 600;\n          margin-right: 1rem;\n        }\n\n        .admin-info {\n          display: flex;\n          align-items: center;\n        }\n\n        .admin-details h4 {\n          margin: 0;\n          font-size: 1rem;\n          font-weight: 600;\n          color: #1e293b;\n        }\n\n        .admin-details p {\n          margin: 0;\n          font-size: 0.875rem;\n          color: #64748b;\n        }\n\n        .admin-actions {\n          display: flex;\n          gap: 0.5rem;\n        }\n\n        .btn-icon {\n          padding: 0.5rem;\n          border: none;\n          border-radius: 0.375rem;\n          cursor: pointer;\n          transition: all 0.2s;\n        }\n\n        .btn-delete {\n          background: #ef4444;\n          color: white;\n        }\n\n        .btn-delete:hover {\n          background: #dc2626;\n        }\n\n        .btn-delete:disabled {\n          background: #d1d5db;\n          cursor: not-allowed;\n        }\n\n        .admin-form-modal {\n          position: fixed;\n          top: 0;\n          left: 0;\n          right: 0;\n          bottom: 0;\n          z-index: 1000;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          padding: 1rem;\n        }\n\n        .modal-backdrop {\n          position: absolute;\n          top: 0;\n          left: 0;\n          right: 0;\n          bottom: 0;\n          background: rgba(0, 0, 0, 0.5);\n        }\n\n        .modal-content {\n          background: white;\n          border-radius: 0.75rem;\n          box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);\n          width: 100%;\n          max-width: 500px;\n          max-height: 90vh;\n          overflow-y: auto;\n          position: relative;\n          z-index: 1;\n        }\n\n        .modal-header {\n          display: flex;\n          justify-content: space-between;\n          align-items: center;\n          padding: 1.5rem;\n          border-bottom: 1px solid #e5e7eb;\n        }\n\n        .modal-header h3 {\n          margin: 0;\n          font-size: 1.25rem;\n          font-weight: 600;\n          color: #1e293b;\n        }\n\n        .modal-close {\n          background: none;\n          border: none;\n          color: #6b7280;\n          cursor: pointer;\n          padding: 0.5rem;\n          border-radius: 0.375rem;\n          transition: all 0.2s;\n        }\n\n        .modal-close:hover {\n          background: #f3f4f6;\n          color: #374151;\n        }\n\n        .admin-form {\n          padding: 1.5rem;\n        }\n\n        .password-input-container {\n          position: relative;\n        }\n\n        .password-input {\n          padding-right: 3rem;\n        }\n\n        .password-toggle {\n          position: absolute;\n          right: 0.75rem;\n          top: 50%;\n          transform: translateY(-50%);\n          background: none;\n          border: none;\n          color: #6b7280;\n          cursor: pointer;\n          padding: 0.25rem;\n          border-radius: 0.25rem;\n          transition: color 0.2s;\n        }\n\n        .password-toggle:hover {\n          color: #374151;\n        }\n\n        .form-text {\n          color: #6b7280;\n          font-size: 0.75rem;\n          margin-top: 0.25rem;\n        }\n\n        .form-actions {\n          display: flex;\n          justify-content: flex-end;\n          gap: 1rem;\n          margin-top: 2rem;\n          padding-top: 1.5rem;\n          border-top: 1px solid #e5e7eb;\n        }\n\n        .empty-state {\n          padding: 3rem;\n          text-align: center;\n          color: #64748b;\n        }\n\n        .empty-state i {\n          font-size: 3rem;\n          margin-bottom: 1rem;\n          color: #d1d5db;\n        }\n\n        .current-user {\n          background: #fef3c7;\n          color: #92400e;\n        }\n\n        @media (max-width: 768px) {\n          .page-header {\n            flex-direction: column;\n            align-items: stretch;\n          }\n\n          .modal-content {\n            margin: 1rem;\n            max-width: none;\n          }\n\n          .admin-table {\n            font-size: 0.875rem;\n          }\n\n          .admin-table th,\n          .admin-table td {\n            padding: 0.75rem 0.5rem;\n          }\n\n          .admin-info {\n            flex-direction: column;\n            align-items: flex-start;\n          }\n\n          .admin-avatar {\n            margin-right: 0;\n            margin-bottom: 0.5rem;\n          }\n        }\n      </style>\n    '}},{key:"bindEvents",value:function(){var n=this,e=document.getElementById("addAdminBtn"),t=document.getElementById("closeFormBtn"),r=document.getElementById("cancelFormBtn"),a=document.getElementById("adminForm"),i=document.getElementById("adminFormModal"),o=null==i?void 0:i.querySelector(".modal-backdrop"),s=document.getElementById("passwordToggle"),c=document.getElementById("adminPassword"),d=document.getElementById("passwordToggleIcon");null==e||e.addEventListener("click",function(){return n.showAdminForm()}),null==t||t.addEventListener("click",function(){return n.hideAdminForm()}),null==r||r.addEventListener("click",function(){return n.hideAdminForm()}),null==o||o.addEventListener("click",function(){return n.hideAdminForm()}),null==a||a.addEventListener("submit",function(e){return n.handleFormSubmit(e)}),null==s||s.addEventListener("click",function(){var n="password"===c.type;c.type=n?"text":"password",d.className=n?"fas fa-eye-slash":"fas fa-eye"})}},{key:"loadAdmins",value:(t=(0,A.A)(w().mark(function n(){var e;return w().wrap(function(n){for(;;)switch(n.prev=n.next){case 0:return n.prev=0,n.next=1,this.apiService.get("/admins");case 1:e=n.sent,this.admins=e.data||[],this.renderAdminList(),n.next=3;break;case 2:n.prev=2,n.catch(0),this.notificationService.error("Error","Failed to load admin users"),this.renderError();case 3:case"end":return n.stop()}},n,this,[[0,2]])})),function(){return t.apply(this,arguments)})},{key:"renderAdminList",value:function(){var n=this,e=document.getElementById("adminList");if(e)if(0!==this.admins.length){var t=this.getCurrentUser(),r='\n      <table class="admin-table">\n        <thead>\n          <tr>\n            <th>Admin User</th>\n            <th>Created</th>\n            <th>Last Updated</th>\n            <th>Actions</th>\n          </tr>\n        </thead>\n        <tbody>\n          '.concat(this.admins.map(function(e){return'\n            <tr class="'.concat(t&&t.username===e.username?"current-user":"",'">\n              <td>\n                <div class="admin-info">\n                  <div class="admin-avatar">\n                    ').concat(e.username.charAt(0).toUpperCase(),'\n                  </div>\n                  <div class="admin-details">\n                    <h4>').concat(n.escapeHtml(e.username),"</h4>\n                    <p>").concat(t&&t.username===e.username?"Current User":"Administrator","</p>\n                  </div>\n                </div>\n              </td>\n              <td>").concat(new Date(e.created_at).toLocaleDateString(),"</td>\n              <td>").concat(new Date(e.updated_at).toLocaleDateString(),'</td>\n              <td>\n                <div class="admin-actions">\n                  <button \n                    class="btn-icon btn-delete" \n                    onclick="window.adminManagementPage.deleteAdmin(').concat(e.id,')"\n                    ').concat(t&&t.username===e.username?'disabled title="Cannot delete current user"':'title="Delete Admin"','\n                  >\n                    <i class="fas fa-trash"></i>\n                  </button>\n                </div>\n              </td>\n            </tr>\n          ')}).join(""),"\n        </tbody>\n      </table>\n    ");e.innerHTML=r,window.adminManagementPage=this}else e.innerHTML='\n        <div class="empty-state">\n          <i class="fas fa-users-cog"></i>\n          <h3>No Admin Users Found</h3>\n          <p>Start by adding your first admin user to manage the system.</p>\n        </div>\n      '}},{key:"renderError",value:function(){var n=document.getElementById("adminList");n&&(n.innerHTML='\n        <div class="empty-state">\n          <i class="fas fa-exclamation-triangle"></i>\n          <h3>Error Loading Admin Users</h3>\n          <p>There was an error loading the admin users. Please try again.</p>\n          <button class="btn btn-primary" onclick="window.location.reload()">Retry</button>\n        </div>\n      ')}},{key:"showAdminForm",value:function(){var n=document.getElementById("adminFormModal");document.getElementById("adminForm").reset(),n.style.display="flex",document.body.style.overflow="hidden"}},{key:"hideAdminForm",value:function(){document.getElementById("adminFormModal").style.display="none",document.body.style.overflow=""}},{key:"handleFormSubmit",value:(e=(0,A.A)(w().mark(function n(e){var t,r,a,i,o,s,c,d,l;return w().wrap(function(n){for(;;)switch(n.prev=n.next){case 0:if(e.preventDefault(),t=document.getElementById("saveAdminBtn"),r=t.querySelector(".btn-text"),a=t.querySelector(".btn-loading"),i=new FormData(e.target),o=i.get("username"),s=i.get("password"),c=i.get("passwordConfirm"),s===c){n.next=1;break}return this.notificationService.error("Validation Error","Passwords do not match"),n.abrupt("return");case 1:if(!(s.length<8)){n.next=2;break}return this.notificationService.error("Validation Error","Password must be at least 8 characters long"),n.abrupt("return");case 2:return d={username:o,password:s},t.disabled=!0,r.classList.add("d-none"),a.classList.remove("d-none"),n.prev=3,n.next=4,this.apiService.post("/admins",d);case 4:return this.notificationService.success("Success","Admin user created successfully"),this.hideAdminForm(),n.next=5,this.loadAdmins();case 5:n.next=7;break;case 6:n.prev=6,l=n.catch(3),this.notificationService.error("Error",l.message||"Failed to create admin user");case 7:return n.prev=7,t.disabled=!1,r.classList.remove("d-none"),a.classList.add("d-none"),n.finish(7);case 8:case"end":return n.stop()}},n,this,[[3,6,7,8]])})),function(n){return e.apply(this,arguments)})},{key:"deleteAdmin",value:(n=(0,A.A)(w().mark(function n(e){var t,r,a;return w().wrap(function(n){for(;;)switch(n.prev=n.next){case 0:if(t=this.admins.find(function(n){return n.id===e})){n.next=1;break}return n.abrupt("return");case 1:if(!(r=this.getCurrentUser())||r.username!==t.username){n.next=2;break}return this.notificationService.error("Error","Cannot delete your own admin account"),n.abrupt("return");case 2:if(confirm('Are you sure you want to delete admin user "'.concat(t.username,'"? This action cannot be undone.'))){n.next=3;break}return n.abrupt("return");case 3:return n.prev=3,n.next=4,this.apiService.delete("/admins/".concat(e));case 4:return this.notificationService.success("Success","Admin user deleted successfully"),n.next=5,this.loadAdmins();case 5:n.next=7;break;case 6:n.prev=6,a=n.catch(3),this.notificationService.error("Error",a.message||"Failed to delete admin user");case 7:case"end":return n.stop()}},n,this,[[3,6]])})),function(e){return n.apply(this,arguments)})},{key:"getCurrentUser",value:function(){return window.authService&&window.authService.getUser?window.authService.getUser():null}},{key:"escapeHtml",value:function(n){var e=document.createElement("div");return e.textContent=n,e.innerHTML}}]);var n,e,t,r}();function O(n,e){var t="undefined"!=typeof Symbol&&n[Symbol.iterator]||n["@@iterator"];if(!t){if(Array.isArray(n)||(t=function(n,e){if(n){if("string"==typeof n)return _(n,e);var t={}.toString.call(n).slice(8,-1);return"Object"===t&&n.constructor&&(t=n.constructor.name),"Map"===t||"Set"===t?Array.from(n):"Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t)?_(n,e):void 0}}(n))||e&&n&&"number"==typeof n.length){t&&(n=t);var r=0,a=function(){};return{s:a,n:function(){return r>=n.length?{done:!0}:{done:!1,value:n[r++]}},e:function(n){throw n},f:a}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var i,o=!0,s=!1;return{s:function(){t=t.call(n)},n:function(){var n=t.next();return o=n.done,n},e:function(n){s=!0,i=n},f:function(){try{o||null==t.return||t.return()}finally{if(s)throw i}}}}function _(n,e){(null==e||e>n.length)&&(e=n.length);for(var t=0,r=Array(e);t<e;t++)r[t]=n[t];return r}const R=function(){return(0,v.A)(function n(){(0,b.A)(this,n),this.routes=new Map,this.container=null,this.currentRoute=null,this.authService=new L,this.setupRoutes()},[{key:"setupRoutes",value:function(){this.routes.set("/",{component:I,requiresAuth:!1}),this.routes.set("/login",{component:I,requiresAuth:!1}),this.routes.set("/admin",{component:T,requiresAuth:!0,defaultChild:"/admin/dashboard"}),this.routes.set("/admin/dashboard",{component:P,requiresAuth:!0,parent:"/admin"}),this.routes.set("/admin/products",{component:z,requiresAuth:!0,parent:"/admin"}),this.routes.set("/admin/brands",{component:M,requiresAuth:!0,parent:"/admin"}),this.routes.set("/admin/categories",{component:q,requiresAuth:!0,parent:"/admin"}),this.routes.set("/admin/banners",{component:D,requiresAuth:!0,parent:"/admin"}),this.routes.set("/admin/admins",{component:U,requiresAuth:!0,parent:"/admin"})}},{key:"init",value:function(n){var e=this;this.container=n,window.addEventListener("popstate",function(){e.handleRoute()}),this.handleRoute()}},{key:"navigate",value:function(n){arguments.length>1&&void 0!==arguments[1]&&arguments[1]?window.history.replaceState({},"",n):window.history.pushState({},"",n),this.handleRoute()}},{key:"handleRoute",value:(e=(0,A.A)(w().mark(function n(){var e,t,r,a,i,o,s,c,d,l=this;return w().wrap(function(n){for(;;)switch(n.prev=n.next){case 0:if(e=window.location.pathname,t=this.routes.get(e)){n.next=7;break}r=O(this.routes),n.prev=1,r.s();case 2:if((a=r.n()).done){n.next=4;break}if(i=(0,y.A)(a.value,2),o=i[0],s=i[1],!e.startsWith(o)||!s.defaultChild){n.next=3;break}return t=s,n.abrupt("continue",4);case 3:n.next=2;break;case 4:n.next=6;break;case 5:n.prev=5,d=n.catch(1),r.e(d);case 6:return n.prev=6,r.f(),n.finish(6);case 7:if(t){n.next=8;break}return this.navigate("/login",!0),n.abrupt("return");case 8:return n.next=9,this.authService.checkAuth();case 9:if(c=n.sent,!t.requiresAuth||c){n.next=10;break}return this.navigate("/login",!0),n.abrupt("return");case 10:if(t.requiresAuth||!c||"/login"!==e){n.next=11;break}return this.navigate("/admin/dashboard",!0),n.abrupt("return");case 11:if(!t.defaultChild||e!==Object.keys(Object.fromEntries(this.routes)).find(function(n){return l.routes.get(n)===t})){n.next=12;break}return this.navigate(t.defaultChild,!0),n.abrupt("return");case 12:return n.next=13,this.renderRoute(t,e);case 13:case"end":return n.stop()}},n,this,[[1,5,6,7]])})),function(){return e.apply(this,arguments)})},{key:"renderRoute",value:(n=(0,A.A)(w().mark(function n(e,t){var r,a,i,o,s;return w().wrap(function(n){for(;;)switch(n.prev=n.next){case 0:if(this.container){n.next=1;break}return n.abrupt("return");case 1:if(n.prev=1,this.container.innerHTML="",r=e.component,a=new r,!e.parent){n.next=8;break}if(!(i=this.routes.get(e.parent))){n.next=6;break}return o=new i.component,n.next=2,o.render(this.container);case 2:if(!(s=this.container.querySelector(".admin-content"))){n.next=4;break}return n.next=3,a.render(s);case 3:n.next=5;break;case 4:return n.next=5,a.render(this.container);case 5:n.next=7;break;case 6:return n.next=7,a.render(this.container);case 7:n.next=9;break;case 8:return n.next=9,a.render(this.container);case 9:this.currentRoute={route:e,path:t,component:a},n.next=11;break;case 10:n.prev=10,n.catch(1),this.container.innerHTML='\n        <div style="padding: 2rem; text-align: center;">\n          <h2>Error Loading Page</h2>\n          <p>There was an error loading this page. Please try again.</p>\n          <button onclick="window.location.reload()" class="btn btn-primary">Reload Page</button>\n        </div>\n      ';case 11:case"end":return n.stop()}},n,this,[[1,10]])})),function(e,t){return n.apply(this,arguments)})},{key:"getCurrentRoute",value:function(){return this.currentRoute}}]);var n,e}();const N=function(){return(0,v.A)(function n(){(0,b.A)(this,n),this.router=new R,this.authService=new L,this.notificationService=new F,this.init()},[{key:"init",value:function(){var n=this;this.notificationService.init(),window.addEventListener("unhandledrejection",function(e){n.notificationService.error("An unexpected error occurred")}),this.authService.onAuthStateChange(function(e){!e&&window.location.pathname.startsWith("/admin")&&n.router.navigate("/login")})}},{key:"mount",value:function(n){this.container=n,this.router.init(n)}}])}();document.addEventListener("DOMContentLoaded",function(){var n=document.getElementById("root");n&&(n.innerHTML="",(new N).mount(n))})}},t={};function r(n){var a=t[n];if(void 0!==a)return a.exports;var i=t[n]={id:n,exports:{}};return e[n](i,i.exports,r),i.exports}r.m=e,n=[],r.O=(e,t,a,i)=>{if(!t){var o=1/0;for(l=0;l<n.length;l++){for(var[t,a,i]=n[l],s=!0,c=0;c<t.length;c++)(!1&i||o>=i)&&Object.keys(r.O).every(n=>r.O[n](t[c]))?t.splice(c--,1):(s=!1,i<o&&(o=i));if(s){n.splice(l--,1);var d=a();void 0!==d&&(e=d)}}return e}i=i||0;for(var l=n.length;l>0&&n[l-1][2]>i;l--)n[l]=n[l-1];n[l]=[t,a,i]},r.n=n=>{var e=n&&n.__esModule?()=>n.default:()=>n;return r.d(e,{a:e}),e},r.d=(n,e)=>{for(var t in e)r.o(e,t)&&!r.o(n,t)&&Object.defineProperty(n,t,{enumerable:!0,get:e[t]})},r.o=(n,e)=>Object.prototype.hasOwnProperty.call(n,e),(()=>{var n={792:0};r.O.j=e=>0===n[e];var e=(e,t)=>{var a,i,[o,s,c]=t,d=0;if(o.some(e=>0!==n[e])){for(a in s)r.o(s,a)&&(r.m[a]=s[a]);if(c)var l=c(r)}for(e&&e(t);d<o.length;d++)i=o[d],r.o(n,i)&&n[i]&&n[i][0](),n[i]=0;return r.O(l)},t=self.webpackChunkggcasecatalogs=self.webpackChunkggcasecatalogs||[];t.forEach(e.bind(null,0)),t.push=e.bind(null,t.push.bind(t))})(),r.nc=void 0;var a=r.O(void 0,[96],()=>r(744));a=r.O(a)})();
//# sourceMappingURL=main.e6637e2155ce4d23253f.js.map