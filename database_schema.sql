CREATE DATABASE IF NOT EXISTS gg_catalog_db;
USE gg_catalog_db;

-- Table: admins
CREATE TABLE admins (
  id INT PRIMARY KEY AUTO_INCREMENT,
  username VARCHAR(50) UNIQUE NOT NULL,
  password_hash VARCHAR(255) NOT NULL,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Table: brands
CREATE TABLE brands (
  id INT PRIMARY KEY AUTO_INCREMENT,
  name VARCHAR(100) NOT NULL UNIQUE,
  brand_photo TEXT,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- Table: categories
CREATE TABLE categories (
  id INT PRIMARY KEY AUTO_INCREMENT,
  name VARCHAR(100) NOT NULL UNIQUE,
  category_photo TEXT,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- Table: products
CREATE TABLE products (
  id INT PRIMARY KEY AUTO_INCREMENT,
  name <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
  description TEXT,
  brand_id INT,
  category_id INT,
  total_sold INT DEFAULT 0,
  avg_rating FLOAT DEFAULT 0,
  total_raters INT DEFAULT 0,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (brand_id) REFERENCES brands(id) ON DELETE SET NULL,
  FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE SET NULL
);

-- Table: product_variants
CREATE TABLE product_variants (
  id INT PRIMARY KEY AUTO_INCREMENT,
  product_id INT,
  variant_name VARCHAR(100),
  price DECIMAL(12,2) NOT NULL,
  FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE
);

-- Table: product_photos
CREATE TABLE product_photos (
  id INT PRIMARY KEY AUTO_INCREMENT,
  product_id INT,
  photo_url TEXT,
  FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE
);

-- Table: ratings
CREATE TABLE ratings (
  id INT PRIMARY KEY AUTO_INCREMENT,
  product_id INT,
  star INT CHECK (star >= 1 AND star <= 5),
  review_text TEXT,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE
);

-- Table: web_banners
CREATE TABLE web_banners (
  id INT PRIMARY KEY AUTO_INCREMENT,
  title VARCHAR(255),
  banner_image_url TEXT,
  redirect_url TEXT,
  active BOOLEAN DEFAULT TRUE,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Sample Data (Optional)
INSERT INTO brands (name, brand_photo) VALUES 
('Nike', 'https://example.com/nike-logo.jpg'),
('Adidas', 'https://example.com/adidas-logo.jpg'),
('Apple', 'https://example.com/apple-logo.jpg');

INSERT INTO categories (name, category_photo) VALUES 
('Electronics', 'https://example.com/electronics.jpg'),
('Footwear', 'https://example.com/footwear.jpg'),
('Clothing', 'https://example.com/clothing.jpg');

INSERT INTO products (name, description, brand_id, category_id) VALUES 
('iPhone 15', 'Latest Apple smartphone with advanced features', 3, 1),
('Nike Air Max', 'Comfortable running shoes for athletes', 1, 2),
('Adidas T-Shirt', 'Premium cotton t-shirt for sports', 2, 3);

INSERT INTO product_variants (product_id, variant_name, price) VALUES 
(1, '128GB - Black', 999.99),
(1, '256GB - Blue', 1099.99),
(2, 'Size 42', 129.99),
(2, 'Size 44', 129.99),
(3, 'Size M - Red', 29.99),
(3, 'Size L - Blue', 29.99);

INSERT INTO web_banners (title, banner_image_url, redirect_url, active) VALUES 
('Summer Sale 2024', 'https://example.com/summer-banner.jpg', 'https://example.com/summer-sale', TRUE),
('New Arrivals', 'https://example.com/new-arrivals-banner.jpg', 'https://example.com/new-arrivals', TRUE);

INSERT INTO admins (username, password_hash) VALUES 
('adminUser', '$2b$10$kCFAA78JqbB8vL/MkoYV..tqdaE8OJqfblRg.oXue4x.Qny.KyKxi');